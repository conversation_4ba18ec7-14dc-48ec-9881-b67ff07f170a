# 彩票数据分析系统 - Git忽略文件配置

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
*~
.project
.pydevproject

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 数据库文件
*.db
*.sqlite
*.sqlite3
*.db-journal

# 日志文件
*.log
logs/
*.log.*

# 临时文件
*.tmp
*.temp
*.bak
*.backup
*~

# 配置文件（包含敏感信息）
config/secrets.py
config/local_settings.py
.env
.env.local
.env.development
.env.test
.env.production

# 测试覆盖率
.coverage
.pytest_cache/
htmlcov/
.tox/
.nox/
coverage.xml
*.cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints

# Flask
instance/
.webassets-cache

# 数据文件（根据需要调整）
# data/*.csv
# data/*.xlsx
# data/*.json
# 保留示例数据
!data/sample_*.csv
!data/example_*.json

# 上传文件
uploads/
temp_uploads/

# 缓存文件
.cache/
*.pyc

# 文档生成
docs/_build/

# 包管理
pip-log.txt
pip-delete-this-directory.txt

# 单元测试
.pytest_cache/
.coverage

# MyPy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# 项目特定
# 如果有特定的临时文件或测试文件，在这里添加
test_*.py
*_test_*.py
debug_*.py
temp_*.py
