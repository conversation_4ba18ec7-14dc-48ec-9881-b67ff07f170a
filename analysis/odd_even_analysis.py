"""
奇偶分析模块
"""
from typing import List, Dict, Tuple, Optional
import pandas as pd
from datetime import datetime
from config.lottery_config import LotteryType, LotteryConfig


def calculate_odd_even_ratio(red_balls: List[int]) -> str:
    """
    计算奇偶比

    Args:
        red_balls: 红球号码列表

    Returns:
        str: 奇偶比，格式如"3:2"
    """
    odd_count = sum(1 for ball in red_balls if ball % 2 == 1)
    even_count = len(red_balls) - odd_count
    return f"{odd_count}:{even_count}"


def calculate_odd_even_pattern(red_balls: List[int]) -> str:
    """
    计算奇偶排布模式
    
    Args:
        red_balls: 红球号码列表（已排序）
        
    Returns:
        str: 奇偶排布，如"奇偶奇偶偶"
    """
    pattern = ""
    for ball in red_balls:
        if ball % 2 == 1:
            pattern += "奇"
        else:
            pattern += "偶"
    return pattern


def find_last_same_odd_even_pattern(current_index: int, current_pattern: str, 
                                   all_patterns: List[str], dates: List[str]) -> Tuple[str, int]:
    """
    查找上次相同奇偶排布的日期和间隔
    
    Args:
        current_index: 当前数据索引
        current_pattern: 当前奇偶排布
        all_patterns: 所有奇偶排布列表
        dates: 所有日期列表
        
    Returns:
        Tuple[str, int]: (上次日期, 间隔天数)
    """
    # 从当前位置向前查找
    for i in range(current_index - 1, -1, -1):
        if all_patterns[i] == current_pattern:
            last_date = dates[i]
            current_date = dates[current_index]
            
            # 计算日期间隔
            last_dt = datetime.strptime(last_date, '%Y-%m-%d')
            current_dt = datetime.strptime(current_date, '%Y-%m-%d')
            interval_days = (current_dt - last_dt).days
            
            return last_date, interval_days
    
    return "无", 0


def analyze_odd_even(data: pd.DataFrame, lottery_type: LotteryType) -> List[Dict[str, any]]:
    """
    进行完整的奇偶分析

    Args:
        data: 原始数据DataFrame
        lottery_type: 彩票类型

    Returns:
        List[Dict]: 奇偶分析结果列表
    """
    results = []
    all_patterns = []
    dates = []

    # 获取彩票配置
    config = LotteryConfig.get_config(lottery_type)
    red_ball_columns = config.get('csv_columns', {}).get('red_balls', [])
    date_column = config.get('csv_columns', {}).get('date', '开奖日期')

    # 第一遍：计算所有奇偶排布
    for index, row in data.iterrows():
        try:
            red_balls = []
            for col in red_ball_columns:
                value = row[col]
                if pd.isna(value):
                    raise ValueError(f"空值在列 {col}")
                red_balls.append(int(float(value)))

            date_str = row[date_column].strftime('%Y-%m-%d')

            odd_even_ratio = calculate_odd_even_ratio(red_balls)
            odd_even_pattern = calculate_odd_even_pattern(red_balls)

            all_patterns.append(odd_even_pattern)
            dates.append(date_str)
        except (ValueError, TypeError) as e:
            print(f"警告: 第{index+1}行数据处理失败: {e}, 跳过该记录")
            all_patterns.append("")  # 占位符
            dates.append("")

    # 第二遍：查找上次相同排布
    for index, row in data.iterrows():
        try:
            red_balls = []
            for col in red_ball_columns:
                value = row[col]
                if pd.isna(value):
                    raise ValueError(f"空值在列 {col}")
                red_balls.append(int(float(value)))

            odd_even_ratio = calculate_odd_even_ratio(red_balls)
            odd_even_pattern = calculate_odd_even_pattern(red_balls)

            # 查找上次相同奇偶排布
            last_date, interval = find_last_same_odd_even_pattern(
                index, odd_even_pattern, all_patterns, dates
            )

            last_pattern_info = f"{last_date}({interval}天)" if last_date != "无" else "无"

            result = {
                '奇偶比': odd_even_ratio,
                '奇偶排布': odd_even_pattern,
                '上次奇偶排布': last_pattern_info
            }
        except (ValueError, TypeError) as e:
            print(f"警告: 第{index+1}行数据处理失败: {e}, 使用默认值")
            result = {
                '奇偶比': "0:0",
                '奇偶排布': "无效",
                '上次奇偶排布': "无"
            }

        results.append(result)

    return results


def analyze_odd_even_single(red_balls: List[int], lottery_type: LotteryType) -> Dict[str, str]:
    """
    分析单条记录的奇偶情况

    Args:
        red_balls: 红球号码列表
        lottery_type: 彩票类型

    Returns:
        Dict[str, str]: 奇偶分析结果
    """
    try:
        odd_even_ratio = calculate_odd_even_ratio(red_balls)
        odd_even_pattern = calculate_odd_even_pattern(red_balls)

        return {
            '奇偶比': odd_even_ratio,
            '奇偶排布': odd_even_pattern
        }
    except Exception as e:
        return {
            '奇偶比': "0:0",
            '奇偶排布': "无效"
        }
