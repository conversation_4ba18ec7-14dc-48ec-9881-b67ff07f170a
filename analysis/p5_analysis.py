"""
排列五专用分析模块
实现排列五特有的统计指标计算
"""
from typing import List, Dict, Tuple, Any
import pandas as pd
from datetime import datetime
from config.lottery_config import LotteryType, LotteryConfig


def parse_p5_numbers(numbers_str: str) -> List[int]:
    """
    解析排列五号码字符串
    
    Args:
        numbers_str: 号码字符串，如 "1 2 3 4 5"
        
    Returns:
        List[int]: 号码列表
    """
    try:
        return [int(x) for x in numbers_str.strip().split()]
    except (ValueError, AttributeError):
        raise ValueError(f"无效的号码格式: {numbers_str}")


def calculate_odd_even_ratio(numbers: List[int]) -> str:
    """
    计算奇偶比
    
    Args:
        numbers: 5位号码列表
        
    Returns:
        str: 奇偶比，格式如"3:2"
    """
    odd_count = sum(1 for num in numbers if num % 2 == 1)
    even_count = len(numbers) - odd_count
    return f"{odd_count}:{even_count}"


def calculate_odd_even_pattern(numbers: List[int]) -> str:
    """
    计算奇偶排布
    
    Args:
        numbers: 5位号码列表
        
    Returns:
        str: 奇偶排布，如"奇偶奇偶奇"
    """
    pattern = ""
    for num in numbers:
        pattern += "奇" if num % 2 == 1 else "偶"
    return pattern


def calculate_odd_even_code(numbers: List[int]) -> int:
    """
    计算奇偶码（十进制表示）
    
    Args:
        numbers: 5位号码列表
        
    Returns:
        int: 奇偶码，奇数为1，偶数为0，如奇偶奇偶奇 = 10101 = 21
    """
    binary_str = ""
    for num in numbers:
        binary_str += "1" if num % 2 == 1 else "0"
    return int(binary_str, 2)


def calculate_big_small_ratio(numbers: List[int], threshold: int = 5) -> str:
    """
    计算大小比
    
    Args:
        numbers: 5位号码列表
        threshold: 大小数分界线，默认5（5-9为大数，0-4为小数）
        
    Returns:
        str: 大小比，格式如"3:2"
    """
    big_count = sum(1 for num in numbers if num >= threshold)
    small_count = len(numbers) - big_count
    return f"{big_count}:{small_count}"


def calculate_big_small_pattern(numbers: List[int], threshold: int = 5) -> str:
    """
    计算大小排布
    
    Args:
        numbers: 5位号码列表
        threshold: 大小数分界线，默认5
        
    Returns:
        str: 大小排布，如"大小大小大"
    """
    pattern = ""
    for num in numbers:
        pattern += "大" if num >= threshold else "小"
    return pattern


def calculate_big_small_code(numbers: List[int], threshold: int = 5) -> int:
    """
    计算大小码（十进制表示）
    
    Args:
        numbers: 5位号码列表
        threshold: 大小数分界线，默认5
        
    Returns:
        int: 大小码，大数为1，小数为0
    """
    binary_str = ""
    for num in numbers:
        binary_str += "1" if num >= threshold else "0"
    return int(binary_str, 2)


def calculate_consecutive_count(numbers: List[int]) -> int:
    """
    计算连号个数
    
    Args:
        numbers: 5位号码列表
        
    Returns:
        int: 连号个数（不考虑位置顺序）
    """
    # 对号码排序
    sorted_nums = sorted(numbers)
    consecutive_count = 0
    current_consecutive = 1
    
    for i in range(1, len(sorted_nums)):
        if sorted_nums[i] == sorted_nums[i-1] + 1:
            current_consecutive += 1
        else:
            if current_consecutive >= 2:
                consecutive_count = max(consecutive_count, current_consecutive)
            current_consecutive = 1
    
    # 检查最后一组
    if current_consecutive >= 2:
        consecutive_count = max(consecutive_count, current_consecutive)
    
    return consecutive_count


def calculate_repeat_count(numbers: List[int]) -> int:
    """
    计算重号个数
    
    Args:
        numbers: 5位号码列表
        
    Returns:
        int: 重号个数（重复出现的号码总数）
    """
    from collections import Counter
    counter = Counter(numbers)
    repeat_count = 0
    
    for num, count in counter.items():
        if count > 1:
            repeat_count += count
    
    return repeat_count


def calculate_number_format(numbers: List[int]) -> str:
    """
    计算号码格式

    Args:
        numbers: 5位号码列表

    Returns:
        str: 号码格式，如"AABBC"表示第1、2位相同，第3、4位相同
    """
    from collections import defaultdict

    # 统计每个数字出现的位置
    num_positions = defaultdict(list)
    for i, num in enumerate(numbers):
        num_positions[num].append(i)

    # 生成格式字符串
    format_chars = [''] * 5
    char_index = 0
    chars = 'ABCDEFGHIJ'

    for num, positions in num_positions.items():
        if len(positions) > 1:
            # 重复数字使用相同字符
            for pos in positions:
                format_chars[pos] = chars[char_index]
            char_index += 1
        else:
            # 单独数字使用不同字符
            format_chars[positions[0]] = chars[char_index]
            char_index += 1

    return ''.join(format_chars)


def calculate_number_format_with_color(numbers: List[int]) -> Dict[str, Any]:
    """
    计算号码格式（包含颜色信息）

    Args:
        numbers: 5位号码列表

    Returns:
        Dict: 包含格式字符串和颜色信息的字典
    """
    from collections import defaultdict

    # 统计每个数字出现的位置
    num_positions = defaultdict(list)
    for i, num in enumerate(numbers):
        num_positions[num].append(i)

    # 生成格式字符串和颜色信息
    format_chars = [''] * 5
    color_info = ['normal'] * 5  # normal, repeat
    char_index = 0
    chars = 'ABCDEFGHIJ'

    for num, positions in num_positions.items():
        if len(positions) > 1:
            # 重复数字使用相同字符，标记为重复（淡蓝色）
            for pos in positions:
                format_chars[pos] = chars[char_index]
                color_info[pos] = 'repeat'
            char_index += 1
        else:
            # 单独数字使用不同字符，保持正常颜色
            format_chars[positions[0]] = chars[char_index]
            color_info[positions[0]] = 'normal'
            char_index += 1

    return {
        'format': ''.join(format_chars),
        'colors': color_info,
        'display_format': format_display_with_color(format_chars, color_info)
    }


def format_display_with_color(format_chars: List[str], color_info: List[str]) -> str:
    """
    生成带颜色标记的显示格式

    Args:
        format_chars: 格式字符列表
        color_info: 颜色信息列表

    Returns:
        str: 带颜色标记的格式字符串
    """
    # 对于控制台显示，使用ANSI颜色代码
    # 对于GUI显示，可以使用HTML标记或其他格式
    result = ""
    for char, color in zip(format_chars, color_info):
        if color == 'repeat':
            # 淡蓝色显示重复字符
            result += f"\033[94m{char}\033[0m"  # ANSI淡蓝色
        else:
            result += char

    return result


def analyze_p5_single_record(numbers: List[int], lottery_type: LotteryType) -> Dict[str, Any]:
    """
    分析单条排列五记录

    Args:
        numbers: 5位号码列表
        lottery_type: 彩票类型（应为P5）

    Returns:
        Dict: 分析结果
    """
    if len(numbers) != 5:
        raise ValueError(f"排列五应有5位数字，实际收到{len(numbers)}位")

    # 获取配置
    threshold = LotteryConfig.get_big_small_threshold(lottery_type)

    # 计算号码格式（包含颜色信息）
    format_info = calculate_number_format_with_color(numbers)

    return {
        '奇偶比': calculate_odd_even_ratio(numbers),
        '奇偶排布': calculate_odd_even_pattern(numbers),
        '奇偶码': calculate_odd_even_code(numbers),
        '大小比': calculate_big_small_ratio(numbers, threshold),
        '大小排布': calculate_big_small_pattern(numbers, threshold),
        '大小码': calculate_big_small_code(numbers, threshold),
        '连号': calculate_consecutive_count(numbers),
        '重号': calculate_repeat_count(numbers),
        '号码格式': format_info['format'],
        '格式颜色': format_info['colors'],
        '彩色格式': format_info['display_format']
    }


def find_last_same_pattern(current_index: int, pattern: str, all_patterns: List[str],
                          dates: List[str]) -> Tuple[str, int]:
    """
    查找上次相同模式的日期和间隔

    Args:
        current_index: 当前记录索引
        pattern: 当前模式
        all_patterns: 所有模式列表
        dates: 所有日期列表

    Returns:
        Tuple[str, int]: (上次日期, 间隔天数)
    """
    for i in range(current_index - 1, -1, -1):
        if i < len(all_patterns) and all_patterns[i] == pattern:
            try:
                current_date = datetime.strptime(dates[current_index], '%Y-%m-%d')
                last_date = datetime.strptime(dates[i], '%Y-%m-%d')
                interval = (current_date - last_date).days
                return dates[i], interval
            except (ValueError, IndexError):
                continue

    return "无", 0


def calculate_position_intervals(current_index: int, numbers: List[int],
                               all_numbers_history: List[List[int]]) -> List[int]:
    """
    计算每个位置上号码的出现间隔

    Args:
        current_index: 当前记录索引
        numbers: 当前5位号码
        all_numbers_history: 所有历史号码列表

    Returns:
        List[int]: 5个位置的间隔期数，0表示首次出现
    """
    intervals = []

    for pos in range(5):  # 5个位置
        current_number = numbers[pos]
        interval = 0

        # 从当前位置往前查找
        for i in range(current_index - 1, -1, -1):
            interval += 1
            if (i < len(all_numbers_history) and
                len(all_numbers_history[i]) > pos and
                all_numbers_history[i][pos] == current_number):
                break
        else:
            # 没有找到，表示首次出现
            interval = 0

        intervals.append(interval)

    return intervals


def format_position_intervals(intervals: List[int]) -> str:
    """
    格式化位置间隔为字符串（对齐格式）

    Args:
        intervals: 5个位置的间隔期数

    Returns:
        str: 格式化的间隔字符串，如"03-00-15-07-02"（每位2位数对齐）
    """
    # 将每个间隔格式化为2位数字，不足补0
    formatted_intervals = [f"{interval:02d}" for interval in intervals]
    return "-".join(formatted_intervals)


def analyze_p5_data(data: pd.DataFrame, lottery_type: LotteryType) -> List[Dict[str, Any]]:
    """
    分析排列五数据
    
    Args:
        data: 原始数据DataFrame
        lottery_type: 彩票类型
        
    Returns:
        List[Dict]: 分析结果列表
    """
    if lottery_type != LotteryType.P5:
        raise ValueError("此函数仅支持排列五数据分析")
    
    results = []
    config = LotteryConfig.get_config(lottery_type)
    csv_columns = config.get('csv_columns', {})
    
    # 获取列名
    date_column = csv_columns.get('date', 'date')
    period_column = csv_columns.get('period', 'period')
    numbers_column = csv_columns.get('numbers', 'numbers')
    
    # 第一遍：收集所有模式和号码用于历史查找
    all_odd_even_patterns = []
    all_big_small_patterns = []
    all_dates = []
    all_numbers_history = []

    for index, row in data.iterrows():
        try:
            numbers = parse_p5_numbers(row[numbers_column])
            date_str = pd.to_datetime(row[date_column]).strftime('%Y-%m-%d')

            analysis_result = analyze_p5_single_record(numbers, lottery_type)
            all_odd_even_patterns.append(analysis_result['奇偶排布'])
            all_big_small_patterns.append(analysis_result['大小排布'])
            all_dates.append(date_str)
            all_numbers_history.append(numbers)

        except (ValueError, TypeError) as e:
            print(f"警告: 第{index+1}行数据处理失败: {e}, 跳过该记录")
            all_odd_even_patterns.append("")
            all_big_small_patterns.append("")
            all_dates.append("")
            all_numbers_history.append([0, 0, 0, 0, 0])  # 默认值
    
    # 第二遍：生成完整分析结果
    for index, row in data.iterrows():
        try:
            numbers = parse_p5_numbers(row[numbers_column])
            date_str = pd.to_datetime(row[date_column]).strftime('%Y-%m-%d')
            period = str(row[period_column])
            
            # 基础分析
            analysis_result = analyze_p5_single_record(numbers, lottery_type)
            
            # 查找历史相同模式
            last_odd_even_date, odd_even_interval = find_last_same_pattern(
                index, analysis_result['奇偶排布'], all_odd_even_patterns, all_dates
            )
            
            last_big_small_date, big_small_interval = find_last_same_pattern(
                index, analysis_result['大小排布'], all_big_small_patterns, all_dates
            )

            # 计算位置间隔
            position_intervals = calculate_position_intervals(index, numbers, all_numbers_history)
            interval_str = format_position_intervals(position_intervals)

            # 组装结果
            result = {
                '日期': date_str,
                '期号': period,
                '号码1': numbers[0],
                '号码2': numbers[1],
                '号码3': numbers[2],
                '号码4': numbers[3],
                '号码5': numbers[4],
                '奇偶比': analysis_result['奇偶比'],
                '奇偶排布': analysis_result['奇偶排布'],
                '奇偶码': analysis_result['奇偶码'],
                '大小比': analysis_result['大小比'],
                '大小排布': analysis_result['大小排布'],
                '大小码': analysis_result['大小码'],
                '连号': analysis_result['连号'],
                '重号': analysis_result['重号'],
                '号码格式': analysis_result['号码格式'],
                '间隔': interval_str,
                '上次奇偶排布': f"{last_odd_even_date}({odd_even_interval}天)" if last_odd_even_date != "无" else "无",
                '上次大小排布': f"{last_big_small_date}({big_small_interval}天)" if last_big_small_date != "无" else "无"
            }
            
            results.append(result)
            
        except (ValueError, TypeError) as e:
            print(f"警告: 第{index+1}行数据处理失败: {e}, 使用默认值")
            # 添加默认结果
            result = {
                '日期': "无效",
                '期号': "无效",
                '号码1': 0, '号码2': 0, '号码3': 0, '号码4': 0, '号码5': 0,
                '奇偶比': "0:0", '奇偶排布': "无效", '奇偶码': 0,
                '大小比': "0:0", '大小排布': "无效", '大小码': 0,
                '连号': 0, '重号': 0, '号码格式': "无效",
                '间隔': "00-00-00-00-00",
                '上次奇偶排布': "无", '上次大小排布': "无"
            }
            results.append(result)
    
    return results
