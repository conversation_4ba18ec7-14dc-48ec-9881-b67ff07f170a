"""
彩票类型配置模块
"""
from enum import Enum
from typing import Dict, List, Tuple


class LotteryType(Enum):
    """彩票类型枚举"""
    DLT = "dlt"  # 大乐透
    SSQ = "ssq"  # 双色球
    P5 = "p5"    # 排列五


class LotteryConfig:
    """彩票配置管理类"""
    
    # 彩票配置参数
    CONFIG = {
        LotteryType.DLT: {
            'name': '大乐透',
            'red_range': (1, 35),      # 红球号码范围
            'red_count': 5,            # 红球数量
            'blue_range': (1, 12),     # 蓝球号码范围
            'blue_count': 2,           # 蓝球数量
            'zone_sizes': [5, 5, 5, 5, 5, 5, 5],  # 分区大小：7个区间，每区5个号码
            'csv_file': 'data/dlt_data.csv',      # 数据文件路径
            'csv_columns': {           # CSV列名映射
                'issue': '期号',
                'date': '开奖日期',
                'red_balls': ['红球1', '红球2', '红球3', '红球4', '红球5'],
                'blue_balls': ['蓝球1', '蓝球2']
            }
        },
        LotteryType.SSQ: {
            'name': '双色球',
            'red_range': (1, 33),      # 红球号码范围
            'red_count': 6,            # 红球数量
            'blue_range': (1, 16),     # 蓝球号码范围
            'blue_count': 1,           # 蓝球数量
            'zone_sizes': [5, 5, 5, 5, 5, 5, 3],  # 分区大小：前6区各5个，第7区3个
            'csv_file': 'data/ssq_data.csv',      # 数据文件路径
            'csv_columns': {           # CSV列名映射
                'issue': '期号',
                'date': '开奖日期',
                'red_balls': ['红球1', '红球2', '红球3', '红球4', '红球5', '红球6'],
                'blue_balls': ['蓝球1']
            }
        },
        LotteryType.P5: {
            'name': '排列五',
            'number_range': (0, 9),    # 号码范围：0-9
            'position_count': 5,       # 位置数量：5位
            'csv_file': 'data/pl5_3000.csv',  # 数据文件路径
            'csv_columns': {           # CSV列名映射
                'date': 'date',        # 开奖日期
                'period': 'period',    # 期号
                'numbers': 'numbers'   # 号码（空格分隔的字符串）
            },
            # 排列五特有配置
            'big_small_threshold': 5,  # 大小数分界线：5-9为大数，0-4为小数
            'analysis_features': {     # 分析特征配置
                'odd_even': True,      # 奇偶分析
                'big_small': True,     # 大小分析
                'consecutive': True,   # 连号分析
                'repeat': True,        # 重号分析
                'pattern': True        # 号码格式分析
            }
        }
    }
    
    @staticmethod
    def get_config(lottery_type: LotteryType) -> Dict:
        """
        获取指定彩票类型的配置
        
        Args:
            lottery_type: 彩票类型
            
        Returns:
            Dict: 配置字典
        """
        return LotteryConfig.CONFIG.get(lottery_type, {})
    
    @staticmethod
    def get_name(lottery_type: LotteryType) -> str:
        """获取彩票类型名称"""
        config = LotteryConfig.get_config(lottery_type)
        return config.get('name', str(lottery_type.value))
    
    @staticmethod
    def get_red_range(lottery_type: LotteryType) -> Tuple[int, int]:
        """获取红球号码范围"""
        config = LotteryConfig.get_config(lottery_type)
        return config.get('red_range', (1, 35))
    
    @staticmethod
    def get_red_count(lottery_type: LotteryType) -> int:
        """获取红球数量"""
        config = LotteryConfig.get_config(lottery_type)
        return config.get('red_count', 5)
    
    @staticmethod
    def get_blue_range(lottery_type: LotteryType) -> Tuple[int, int]:
        """获取蓝球号码范围"""
        config = LotteryConfig.get_config(lottery_type)
        return config.get('blue_range', (1, 12))
    
    @staticmethod
    def get_blue_count(lottery_type: LotteryType) -> int:
        """获取蓝球数量"""
        config = LotteryConfig.get_config(lottery_type)
        return config.get('blue_count', 2)
    
    @staticmethod
    def get_zone_sizes(lottery_type: LotteryType) -> List[int]:
        """获取分区大小列表"""
        config = LotteryConfig.get_config(lottery_type)
        return config.get('zone_sizes', [5, 5, 5, 5, 5, 5, 5])
    
    @staticmethod
    def get_csv_file(lottery_type: LotteryType) -> str:
        """获取CSV文件路径"""
        config = LotteryConfig.get_config(lottery_type)
        return config.get('csv_file', 'data/dlt_data.csv')
    
    @staticmethod
    def get_csv_columns(lottery_type: LotteryType) -> Dict:
        """获取CSV列名映射"""
        config = LotteryConfig.get_config(lottery_type)
        return config.get('csv_columns', {})
    
    @staticmethod
    def get_all_types() -> List[LotteryType]:
        """获取所有支持的彩票类型"""
        return list(LotteryConfig.CONFIG.keys())
    
    @staticmethod
    def get_type_names() -> List[str]:
        """获取所有彩票类型名称"""
        return [LotteryConfig.get_name(lottery_type) for lottery_type in LotteryConfig.get_all_types()]
    
    @staticmethod
    def get_type_by_name(name: str) -> LotteryType:
        """根据名称获取彩票类型"""
        for lottery_type in LotteryConfig.get_all_types():
            if LotteryConfig.get_name(lottery_type) == name:
                return lottery_type
        return LotteryType.DLT  # 默认返回大乐透

    # 排列五专用配置方法
    @staticmethod
    def get_number_range(lottery_type: LotteryType) -> Tuple[int, int]:
        """获取号码范围（排列五专用）"""
        config = LotteryConfig.get_config(lottery_type)
        return config.get('number_range', (0, 9))

    @staticmethod
    def get_position_count(lottery_type: LotteryType) -> int:
        """获取位置数量（排列五专用）"""
        config = LotteryConfig.get_config(lottery_type)
        return config.get('position_count', 5)

    @staticmethod
    def get_big_small_threshold(lottery_type: LotteryType) -> int:
        """获取大小数分界线（排列五专用）"""
        config = LotteryConfig.get_config(lottery_type)
        return config.get('big_small_threshold', 5)

    @staticmethod
    def get_analysis_features(lottery_type: LotteryType) -> Dict:
        """获取分析特征配置（排列五专用）"""
        config = LotteryConfig.get_config(lottery_type)
        return config.get('analysis_features', {})

    @staticmethod
    def is_p5_type(lottery_type: LotteryType) -> bool:
        """判断是否为排列五类型"""
        return lottery_type == LotteryType.P5
    
    @staticmethod
    def calculate_zone_boundaries(lottery_type: LotteryType) -> List[Tuple[int, int]]:
        """
        计算分区边界
        
        Args:
            lottery_type: 彩票类型
            
        Returns:
            List[Tuple[int, int]]: 分区边界列表，每个元组表示(起始号码, 结束号码)
        """
        zone_sizes = LotteryConfig.get_zone_sizes(lottery_type)
        boundaries = []
        start = 1
        
        for size in zone_sizes:
            end = start + size - 1
            boundaries.append((start, end))
            start = end + 1
        
        return boundaries
