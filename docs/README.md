# 大乐透数据分析系统

基于Python和Flask的彩票数据分析Web应用，支持大乐透、双色球、排列五等多种彩票类型的数据分析。

## 功能特性

- **多彩票支持**: 大乐透、双色球、排列五
- **Web界面**: 响应式Web界面，支持桌面、平板、手机访问
- **彩色显示**: 重复号码彩色标记，直观易懂
- **智能分析**: 奇偶分析、分区分析、连号分析等
- **实时分析**: 输入号码即时获得分析结果
- **数据导入**: 支持CSV格式批量数据导入
- **数据表格**: 支持排序、搜索、分页功能

## 系统架构

采用MVC（Model-View-Controller）设计模式，基于Flask Web框架：

```
project_root/
├── model/                      # 数据模型层
│   ├── data_model.py          # 数据存储和管理
│   └── db_manager.py          # 数据库管理
├── controller/                 # 控制器层
│   ├── main_controller.py     # 业务逻辑协调
├── analysis/                   # 数据分析模块
│   ├── base_info.py           # 基础信息提取
│   ├── odd_even_analysis.py   # 奇偶分析
│   ├── zone_analysis.py       # 分区分析
│   ├── p5_analysis.py         # 排列五分析
│   └── analyzer.py            # 分析器整合
├── webapp/                     # Web应用
│   ├── app.py                 # Flask应用主文件
│   ├── data_logic.py          # Web数据逻辑层
│   ├── templates/             # HTML模板
│   └── run.py                 # Web应用启动脚本
├── tests/                      # 单元测试
└── config/                     # 配置文件
```

## 安装和运行

### 环境要求

- Python 3.7+
- 现代浏览器（Chrome、Firefox、Safari、Edge）

### 依赖安装

```bash
# 安装Python依赖
pip install -r config/requirements.txt

# 或使用conda环境
conda env create -f config/environment.yml
conda activate dlt-analysis-web
```

### 快速启动

**推荐方式 - 使用Web应用启动脚本:**
```bash
cd webapp
python run.py
```

**或直接启动Flask应用:**
```bash
cd webapp
python app.py
```

### 访问应用

启动后在浏览器中访问：
- 本地访问: http://127.0.0.1:5001
- 局域网访问: http://[您的IP]:5001



## 使用指南

### 数据分析
1. 选择彩票类型（大乐透/双色球/排列五）
2. 查看历史开奖数据和分析结果
3. 使用表格的排序、搜索、分页功能

### 实时分析
1. 点击"实时分析"菜单
2. 选择彩票类型
3. 输入号码（用逗号分隔）
4. 点击"立即分析"获得结果

### 数据上传
1. 点击"数据上传"菜单
2. 选择彩票类型
3. 上传符合格式的CSV文件
4. 系统自动分析并入库

## 数据格式

### 大乐透格式
```csv
日期,期号,红球1,红球2,红球3,红球4,红球5,蓝球1,蓝球2
2024-01-01,24001,01,12,23,28,35,03,11
2024-01-03,24002,05,15,18,25,33,02,09
```

### 双色球格式
```csv
日期,期号,红球1,红球2,红球3,红球4,红球5,红球6,蓝球
2024-01-02,24001,01,05,12,18,25,33,08
2024-01-04,24002,03,09,15,21,28,31,12
```

### 排列五格式
```csv
日期,期号,号码1,号码2,号码3,号码4,号码5
2024-01-01,24001,1,2,3,4,5
2024-01-02,24002,6,7,8,9,0
```

## 分析指标说明

### 奇偶分析
- **奇偶比**: 红球中奇数与偶数的比例（如3:2）
- **奇偶排布**: 红球奇偶数的具体排列模式（如"奇偶奇偶偶"）
- **上次奇偶排布**: 上一次出现相同奇偶排布的日期和间隔天数

### 分区分析
- **分区比**: 将1-35分为7个区间（每区5个号码）的分布比例
  - 区1: 1-5, 区2: 6-10, 区3: 11-15, 区4: 16-20
  - 区5: 21-25, 区6: 26-30, 区7: 31-35
- **上次分区比**: 上一次出现相同分区比的日期和间隔天数
- **上次0分区比**: 上一次出现相同数量和位置的0分区的日期和间隔天数

## 测试

运行单元测试：

```bash
python -m pytest tests/
```

或者：

```bash
python -m unittest tests.test_analysis
```

## 技术特点

### 后端技术
- **Flask**: 轻量级Web框架
- **SQLite**: 嵌入式数据库
- **Pandas**: 数据分析处理
- **Python**: 核心开发语言

### 前端技术
- **Bootstrap 5**: 响应式UI框架
- **DataTables**: 数据表格组件
- **jQuery**: JavaScript库
- **Font Awesome**: 图标字体

### 架构特点
1. **MVC架构**: 清晰的分层设计，便于维护和扩展
2. **Web界面**: 响应式设计，支持多设备访问
3. **模块化设计**: 各分析功能独立模块，便于单独测试和修改
4. **错误处理**: 完善的异常处理和用户提示
5. **性能优化**: 使用pandas进行高效数据处理
6. **数据安全**: 本地运行，数据不会上传到外部服务器

## 界面特色

### 彩色号码显示
- 🔴 红色圆球：普通红球号码
- 🔵 蓝色圆球：蓝球号码
- 🟡 黄色圆球：重复号码（高亮显示）

### 响应式设计
- 📱 手机端：优化的触摸界面
- 💻 桌面端：完整功能体验
- 📟 平板端：平衡的显示效果

## 扩展建议

- 添加更多分析指标（如和值分析、跨度分析等）
- 支持数据导出功能
- 添加图表可视化
- 支持数据库存储
- 添加预测功能模块
