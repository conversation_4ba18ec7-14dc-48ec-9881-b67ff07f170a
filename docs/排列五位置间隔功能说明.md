# 排列五位置间隔分析功能

## 功能概述

位置间隔功能是排列五数据分析的重要补充，用于统计每个位置上号码的出现间隔期数。这个功能可以帮助分析师了解特定位置上特定号码的出现规律和周期性。

## 功能特点

### 📊 **核心功能**
- **位置精确统计**：分别统计5个位置上每个号码的出现间隔
- **历史追溯**：查找每个位置上号码上次出现的期数
- **间隔计算**：计算当前期与上次出现期之间的间隔期数
- **格式化显示**：使用"位置1-位置2-位置3-位置4-位置5"格式展示

### 🎯 **分析价值**
- **周期性分析**：识别特定位置号码的出现周期
- **热冷号判断**：通过间隔长短判断号码的热冷程度
- **预测辅助**：为下期号码预测提供参考依据
- **规律发现**：发现位置间隔的统计规律

## 技术实现

### 1. 核心算法

```python
def calculate_position_intervals(current_index: int, numbers: List[int], 
                               all_numbers_history: List[List[int]]) -> List[int]:
    """
    计算每个位置上号码的出现间隔
    
    Args:
        current_index: 当前记录索引
        numbers: 当前5位号码
        all_numbers_history: 所有历史号码列表
        
    Returns:
        List[int]: 5个位置的间隔期数，0表示首次出现
    """
```

### 2. 算法逻辑

1. **遍历5个位置**：分别处理每个位置的号码
2. **历史查找**：从当前期往前查找相同位置的相同号码
3. **间隔计算**：计算当前期与上次出现期的差值
4. **首次处理**：如果是首次出现，间隔设为0

### 3. 数据存储

**数据库字段**：`position_intervals TEXT`
**存储格式**：`"2-13-10-12-3"`（5个位置的间隔用"-"分隔）

## 使用示例

### 📈 **实际案例分析**

**25202期开奖号码：2 5 3 7 3**

| 位置 | 号码 | 间隔期数 | 上次出现期号 | 说明 |
|------|------|----------|--------------|------|
| 位置1 | 2 | 2 | 25200期 | 较短间隔，热号 |
| 位置2 | 5 | 13 | 25189期 | 中等间隔 |
| 位置3 | 3 | 10 | 25192期 | 中等间隔 |
| 位置4 | 7 | 12 | 25190期 | 中等间隔 |
| 位置5 | 3 | 3 | 25199期 | 较短间隔，热号 |

**间隔字符串**：`2-13-10-12-3`

### 🔍 **分析解读**

1. **位置1的数字2**：间隔2期，属于热号，近期出现频繁
2. **位置2的数字5**：间隔13期，属于温号，中等频率
3. **位置3的数字3**：间隔10期，属于温号
4. **位置4的数字7**：间隔12期，属于温号
5. **位置5的数字3**：间隔3期，属于热号，近期活跃

## 界面集成

### 表格显示

排列五数据分析表格新增"间隔"列：

| 期号 | 日期 | 号码 | 连重 | 奇偶比 | 奇偶排布 | 大小比 | 大小排布 | **间隔** | 号码格式 |
|------|------|------|------|--------|----------|--------|----------|----------|----------|
| 25202 | 2025-07-31 | 25373 | 连2重2 | 4:1 | 偶奇奇奇奇 | 2:3 | 小大小大小 | **2-13-10-12-3** | ABCDC |

### 动态适配

- **排列五模式**：显示完整的位置间隔信息
- **大乐透/双色球模式**：显示占位符"-"（暂不支持）
- **列宽自适应**：根据内容长度自动调整列宽

## 数据库结构

### 新增字段

```sql
ALTER TABLE analysis_results ADD COLUMN position_intervals TEXT;
```

### 存储示例

```sql
INSERT INTO analysis_results (
    lottery_type, date, issue, number1, number2, number3, number4, number5,
    odd_even_ratio, odd_even_pattern, big_small_ratio, big_small_pattern,
    consecutive_count, repeat_count, number_format, position_intervals
) VALUES (
    'p5', '2025-07-31', '25202', 2, 5, 3, 7, 3,
    '4:1', '偶奇奇奇奇', '2:3', '小大小大小',
    2, 2, 'ABCDC', '2-13-10-12-3'
);
```

## 应用场景

### 1. 号码选择策略

- **热号追踪**：选择间隔较短的号码（如间隔1-5期）
- **冷号回补**：关注间隔较长的号码（如间隔15期以上）
- **温号平衡**：选择中等间隔的号码（如间隔6-14期）

### 2. 位置分析

- **位置热度**：分析各位置的平均间隔
- **位置规律**：发现特定位置的间隔模式
- **位置预测**：基于间隔规律预测下期号码

### 3. 组合策略

- **间隔组合**：选择不同间隔长度的号码组合
- **平衡配置**：在热号、温号、冷号之间平衡选择
- **周期判断**：结合间隔周期制定投注策略

## 技术优势

### 1. 精确计算
- **位置独立**：每个位置独立计算，避免混淆
- **历史完整**：基于完整历史数据计算
- **实时更新**：新数据自动更新间隔统计

### 2. 高效存储
- **紧凑格式**：使用字符串格式节省存储空间
- **快速查询**：支持数据库快速查询和索引
- **兼容性好**：与现有数据结构完全兼容

### 3. 易于扩展
- **算法通用**：可扩展到其他彩票类型
- **格式灵活**：支持不同的显示格式
- **接口统一**：与现有分析框架无缝集成

## 使用指南

### 1. 查看间隔数据

1. 启动排列五分析系统
2. 导入或加载排列五数据
3. 在分析结果表格中查看"间隔"列
4. 间隔格式为"位置1-位置2-位置3-位置4-位置5"

### 2. 分析间隔规律

1. **短期间隔**（1-5期）：热号，近期活跃
2. **中期间隔**（6-14期）：温号，正常频率
3. **长期间隔**（15期以上）：冷号，可能回补

### 3. 制定选号策略

1. 结合间隔数据和其他指标
2. 平衡选择不同间隔长度的号码
3. 关注异常间隔的号码变化
4. 建立个人的间隔分析模型

## 总结

位置间隔功能为排列五分析增加了重要的时间维度，通过精确统计每个位置上号码的出现间隔，为彩票分析师提供了新的分析视角和预测依据。结合其他分析指标，可以构建更加完善的排列五分析体系。
