#!/bin/bash
# 彩票数据分析系统 - Git工作流脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${BLUE}🚀 Git工作流脚本${NC}"
    echo -e "${BLUE}==================${NC}"
    echo ""
    echo "用法: ./git_workflow.sh [命令]"
    echo ""
    echo "可用命令:"
    echo -e "  ${GREEN}status${NC}     - 显示仓库状态"
    echo -e "  ${GREEN}sync${NC}       - 同步远程仓库"
    echo -e "  ${GREEN}commit${NC}     - 快速提交更改"
    echo -e "  ${GREEN}push${NC}       - 推送到远程仓库"
    echo -e "  ${GREEN}feature${NC}    - 创建功能分支"
    echo -e "  ${GREEN}cleanup${NC}    - 清理本地分支"
    echo -e "  ${GREEN}backup${NC}     - 备份当前状态"
    echo -e "  ${GREEN}help${NC}       - 显示此帮助信息"
    echo ""
}

# 显示仓库状态
show_status() {
    echo -e "${BLUE}📊 仓库状态${NC}"
    echo -e "${BLUE}============${NC}"
    
    # 当前分支
    current_branch=$(git branch --show-current)
    echo -e "当前分支: ${GREEN}$current_branch${NC}"
    
    # 远程状态
    echo -e "\n${YELLOW}远程仓库状态:${NC}"
    git remote -v
    
    # 工作区状态
    echo -e "\n${YELLOW}工作区状态:${NC}"
    git status --short
    
    # 最近提交
    echo -e "\n${YELLOW}最近提交:${NC}"
    git log --oneline -5
}

# 同步远程仓库
sync_remote() {
    echo -e "${BLUE}🔄 同步远程仓库${NC}"
    echo -e "${BLUE}=================${NC}"
    
    echo "拉取远程更新..."
    git fetch origin
    
    current_branch=$(git branch --show-current)
    echo "合并远程 $current_branch 分支..."
    git pull origin $current_branch
    
    echo -e "${GREEN}✅ 同步完成${NC}"
}

# 快速提交
quick_commit() {
    echo -e "${BLUE}📝 快速提交${NC}"
    echo -e "${BLUE}============${NC}"
    
    # 检查是否有更改
    if [[ -z $(git status --porcelain) ]]; then
        echo -e "${YELLOW}⚠️  没有需要提交的更改${NC}"
        return
    fi
    
    # 显示更改
    echo -e "${YELLOW}待提交的更改:${NC}"
    git status --short
    
    # 输入提交信息
    echo ""
    read -p "请输入提交信息: " commit_message
    
    if [[ -z "$commit_message" ]]; then
        echo -e "${RED}❌ 提交信息不能为空${NC}"
        return
    fi
    
    # 添加所有更改
    git add -A
    
    # 提交
    git commit -m "$commit_message"
    
    echo -e "${GREEN}✅ 提交完成${NC}"
}

# 推送到远程
push_remote() {
    echo -e "${BLUE}🚀 推送到远程${NC}"
    echo -e "${BLUE}===============${NC}"
    
    current_branch=$(git branch --show-current)
    
    echo "推送 $current_branch 分支到远程..."
    git push origin $current_branch
    
    echo -e "${GREEN}✅ 推送完成${NC}"
}

# 创建功能分支
create_feature() {
    echo -e "${BLUE}🌿 创建功能分支${NC}"
    echo -e "${BLUE}=================${NC}"
    
    read -p "请输入功能分支名称 (feature/): " feature_name
    
    if [[ -z "$feature_name" ]]; then
        echo -e "${RED}❌ 分支名称不能为空${NC}"
        return
    fi
    
    branch_name="feature/$feature_name"
    
    # 确保在主分支
    git checkout master
    git pull origin master
    
    # 创建并切换到新分支
    git checkout -b $branch_name
    
    echo -e "${GREEN}✅ 功能分支 $branch_name 创建完成${NC}"
}

# 清理本地分支
cleanup_branches() {
    echo -e "${BLUE}🧹 清理本地分支${NC}"
    echo -e "${BLUE}=================${NC}"
    
    # 显示所有分支
    echo -e "${YELLOW}本地分支:${NC}"
    git branch
    
    echo ""
    echo -e "${YELLOW}已合并的分支:${NC}"
    merged_branches=$(git branch --merged | grep -v "\*\|master\|main")
    
    if [[ -z "$merged_branches" ]]; then
        echo "没有需要清理的分支"
        return
    fi
    
    echo "$merged_branches"
    
    echo ""
    read -p "是否删除这些已合并的分支? (y/N): " confirm
    
    if [[ $confirm == [yY] ]]; then
        echo "$merged_branches" | xargs -n 1 git branch -d
        echo -e "${GREEN}✅ 分支清理完成${NC}"
    else
        echo "取消清理"
    fi
}

# 备份当前状态
backup_state() {
    echo -e "${BLUE}💾 备份当前状态${NC}"
    echo -e "${BLUE}=================${NC}"
    
    timestamp=$(date +"%Y%m%d_%H%M%S")
    backup_branch="backup/state_$timestamp"
    
    # 创建备份分支
    git checkout -b $backup_branch
    
    # 如果有未提交的更改，先提交
    if [[ ! -z $(git status --porcelain) ]]; then
        git add -A
        git commit -m "🔒 自动备份: $timestamp"
    fi
    
    # 推送备份分支
    git push origin $backup_branch
    
    # 返回原分支
    git checkout master
    
    echo -e "${GREEN}✅ 备份完成: $backup_branch${NC}"
}

# 主函数
main() {
    case "$1" in
        "status")
            show_status
            ;;
        "sync")
            sync_remote
            ;;
        "commit")
            quick_commit
            ;;
        "push")
            push_remote
            ;;
        "feature")
            create_feature
            ;;
        "cleanup")
            cleanup_branches
            ;;
        "backup")
            backup_state
            ;;
        "help"|"")
            show_help
            ;;
        *)
            echo -e "${RED}❌ 未知命令: $1${NC}"
            echo ""
            show_help
            ;;
    esac
}

# 检查是否在Git仓库中
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo -e "${RED}❌ 当前目录不是Git仓库${NC}"
    exit 1
fi

# 执行主函数
main "$1"
