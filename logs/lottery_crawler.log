2025-08-03 04:10:35.063 | INFO | crawlers.base_crawler:__init__:39 - 初始化双色球爬取器
2025-08-03 04:10:35.571 | INFO | crawlers.ssq_dlt_crawler:get_latest_period_from_web:38 - 从网页获取到双色球最新期号: 25087
2025-08-03 04:10:38.974 | INFO | crawlers.base_crawler:incremental_update:194 - 开始增量更新双色球数据...
2025-08-03 04:10:38.993 | INFO | crawlers.base_crawler:get_latest_period_from_file:78 - 本地数据最新期号: 25086
2025-08-03 04:10:39.298 | INFO | crawlers.ssq_dlt_crawler:get_latest_period_from_web:38 - 从网页获取到双色球最新期号: 25087
2025-08-03 04:10:39.299 | INFO | crawlers.base_crawler:incremental_update:215 - 需要更新期号 25087 到 25087
2025-08-03 04:10:39.300 | INFO | crawlers.ssq_dlt_crawler:batch_crawl:65 - 正在爬取双色球数据: https://datachart.500.com/ssq/history/newinc/history.php?start=25087&end=25087
2025-08-03 04:10:39.646 | INFO | crawlers.ssq_dlt_crawler:batch_crawl:133 - 成功爬取双色球 1 期数据
2025-08-03 04:10:39.707 | INFO | utils.common:save_dataframe:194 - 数据已保存到: data/ssq_data.csv
2025-08-03 04:10:39.707 | INFO | crawlers.base_crawler:save_data:178 - 双色球数据已保存，共 3333 期
2025-08-03 04:10:39.709 | INFO | crawlers.base_crawler:incremental_update:224 - 增量更新完成，新增 1 期数据
2025-08-03 04:10:39.709 | INFO | crawlers.base_crawler:__init__:39 - 初始化大乐透爬取器
2025-08-03 04:10:40.217 | INFO | crawlers.ssq_dlt_crawler:get_latest_period_from_web:38 - 从网页获取到大乐透最新期号: 25087
2025-08-03 04:10:44.270 | INFO | crawlers.base_crawler:incremental_update:194 - 开始增量更新大乐透数据...
2025-08-03 04:10:44.280 | INFO | crawlers.base_crawler:get_latest_period_from_file:78 - 本地数据最新期号: 25085
2025-08-03 04:10:44.977 | INFO | crawlers.ssq_dlt_crawler:get_latest_period_from_web:38 - 从网页获取到大乐透最新期号: 25087
2025-08-03 04:10:44.978 | INFO | crawlers.base_crawler:incremental_update:215 - 需要更新期号 25086 到 25087
2025-08-03 04:10:44.978 | INFO | crawlers.ssq_dlt_crawler:batch_crawl:65 - 正在爬取大乐透数据: https://datachart.500.com/dlt/history/newinc/history.php?start=25086&end=25087
2025-08-03 04:10:45.356 | INFO | crawlers.ssq_dlt_crawler:batch_crawl:133 - 成功爬取大乐透 2 期数据
2025-08-03 04:10:45.430 | INFO | utils.common:save_dataframe:194 - 数据已保存到: data/dlt_data.csv
2025-08-03 04:10:45.430 | INFO | crawlers.base_crawler:save_data:178 - 大乐透数据已保存，共 2755 期
2025-08-03 04:10:45.432 | INFO | crawlers.base_crawler:incremental_update:224 - 增量更新完成，新增 2 期数据
2025-08-03 04:10:45.432 | INFO | crawlers.base_crawler:__init__:39 - 初始化排列五爬取器
2025-08-03 04:10:45.962 | INFO | crawlers.pl5_crawler:get_latest_period_from_web:53 - 从网页 https://kaijiang.500.com/plw.shtml 获取到最新期号: 25204
2025-08-03 04:10:46.950 | INFO | crawlers.base_crawler:incremental_update:194 - 开始增量更新排列五数据...
2025-08-03 04:10:46.951 | INFO | utils.common:load_dataframe:235 - 文件不存在: data/pl5_3000_data.csv
2025-08-03 04:10:46.951 | INFO | crawlers.base_crawler:get_latest_period_from_file:81 - 本地数据文件为空或不存在
2025-08-03 04:10:47.131 | INFO | crawlers.pl5_crawler:get_latest_period_from_web:53 - 从网页 https://kaijiang.500.com/plw.shtml 获取到最新期号: 25204
2025-08-03 04:10:47.133 | INFO | crawlers.base_crawler:incremental_update:209 - 本地无数据，从期号 25105 开始爬取
2025-08-03 04:10:47.134 | INFO | crawlers.pl5_crawler:batch_crawl:260 - 开始批量爬取排列五期号 25105 到 25204
2025-08-03 04:10:47.805 | INFO | crawlers.pl5_crawler:_crawl_from_iframe:172 - 从iframe获取到 30 期数据
2025-08-03 04:10:47.807 | INFO | crawlers.pl5_crawler:batch_crawl:271 - iframe数据源可提供期号范围: 25175 - 25204 (共30期)
2025-08-03 04:10:47.807 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25204: 8 3 0 7 4
2025-08-03 04:10:47.808 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25203: 7 3 5 1 6
2025-08-03 04:10:47.808 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25202: 2 5 3 7 3
2025-08-03 04:10:47.809 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25201: 4 0 1 3 0
2025-08-03 04:10:47.810 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25200: 2 1 1 2 9
2025-08-03 04:10:47.810 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25199: 4 8 5 9 3
2025-08-03 04:10:47.812 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25198: 7 1 7 8 3
2025-08-03 04:10:47.812 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25197: 8 6 9 2 0
2025-08-03 04:10:47.812 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25196: 9 4 1 4 0
2025-08-03 04:10:47.813 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25195: 2 1 0 9 0
2025-08-03 04:10:47.814 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25194: 2 7 0 3 7
2025-08-03 04:10:47.815 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25193: 2 6 5 4 9
2025-08-03 04:10:47.815 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25192: 6 8 3 5 7
2025-08-03 04:10:47.816 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25191: 6 6 9 4 0
2025-08-03 04:10:47.817 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25190: 4 9 9 7 2
2025-08-03 04:10:47.817 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25189: 5 5 6 5 1
2025-08-03 04:10:47.818 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25188: 6 2 3 7 3
2025-08-03 04:10:47.818 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25187: 6 4 1 6 0
2025-08-03 04:10:47.819 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25186: 5 1 4 2 3
2025-08-03 04:10:47.820 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25185: 3 0 8 7 3
2025-08-03 04:10:47.820 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25184: 2 2 1 8 7
2025-08-03 04:10:47.821 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25183: 1 7 7 5 9
2025-08-03 04:10:47.822 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25182: 7 2 1 4 6
2025-08-03 04:10:47.823 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25181: 8 9 4 5 8
2025-08-03 04:10:47.823 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25180: 8 8 9 5 7
2025-08-03 04:10:47.824 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25179: 9 7 2 1 6
2025-08-03 04:10:47.825 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25178: 7 1 4 3 6
2025-08-03 04:10:47.827 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25177: 9 7 7 9 9
2025-08-03 04:10:47.827 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25176: 9 4 9 4 6
2025-08-03 04:10:47.828 | INFO | crawlers.pl5_crawler:batch_crawl:277 - 从iframe获取期号 25175: 4 1 3 3 9
2025-08-03 04:10:47.829 | WARNING | crawlers.pl5_crawler:batch_crawl:282 - ⚠️  排列五数据源限制说明:
2025-08-03 04:10:47.829 | WARNING | crawlers.pl5_crawler:batch_crawl:283 -    - 请求期号范围: 25105 - 25204 (共100期)
2025-08-03 04:10:47.830 | WARNING | crawlers.pl5_crawler:batch_crawl:284 -    - iframe可提供: 25175 - 25204 (共30期)
2025-08-03 04:10:47.831 | WARNING | crawlers.pl5_crawler:batch_crawl:285 -    - 排列五只能获取最新约30期的真实数据
2025-08-03 04:10:47.831 | WARNING | crawlers.pl5_crawler:batch_crawl:286 -    - 建议使用增量更新模式，或指定较小的期号范围
2025-08-03 04:10:47.832 | WARNING | crawlers.pl5_crawler:batch_crawl:295 - ❌ 缺失期号过多(70期)，不生成模拟数据
2025-08-03 04:10:47.833 | WARNING | crawlers.pl5_crawler:batch_crawl:296 - 💡 建议：
2025-08-03 04:10:47.834 | WARNING | crawlers.pl5_crawler:batch_crawl:297 -    1. 使用增量更新: --incremental
2025-08-03 04:10:47.834 | WARNING | crawlers.pl5_crawler:batch_crawl:298 -    2. 指定iframe可提供的期号范围: --start 25175 --end 25204
2025-08-03 04:10:47.835 | WARNING | crawlers.pl5_crawler:batch_crawl:299 -    3. 爬取最新30期: --count 30
2025-08-03 04:10:47.836 | WARNING | crawlers.pl5_crawler:batch_crawl:300 -    4. 排列五数据源限制，只能获取最新约30期真实数据
2025-08-03 04:10:47.836 | INFO | crawlers.pl5_crawler:batch_crawl:303 - 仅返回真实数据，共 30 期
2025-08-03 04:10:47.837 | INFO | crawlers.pl5_crawler:batch_crawl:331 - 批量爬取完成，共获取 30 期数据
2025-08-03 04:10:47.839 | INFO | utils.common:load_dataframe:235 - 文件不存在: data/pl5_3000_data.csv
2025-08-03 04:10:47.842 | INFO | utils.common:save_dataframe:194 - 数据已保存到: data/pl5_3000_data.csv
2025-08-03 04:10:47.843 | INFO | crawlers.base_crawler:save_data:178 - 排列五数据已保存，共 30 期
2025-08-03 04:10:47.844 | INFO | crawlers.base_crawler:incremental_update:224 - 增量更新完成，新增 30 期数据
