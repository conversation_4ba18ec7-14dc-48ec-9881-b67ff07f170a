"""
数据模型模块 - 负责数据加载、存储和基础数据操作
"""
import pandas as pd
from datetime import datetime
from typing import List, Dict, Tuple, Optional
import os
from model.db_manager import SQLiteManager
from config.lottery_config import LotteryType, LotteryConfig


class LotteryDataModel:
    """彩票数据模型类"""

    def __init__(self):
        self.data: Optional[pd.DataFrame] = None
        self.analyzed_data: Optional[pd.DataFrame] = None
        self.current_lottery_type: LotteryType = LotteryType.DLT  # 默认大乐透
        self.db_manager = SQLiteManager()

        # 启动时尝试从数据库加载数据
        self.load_from_database()
        
    def load_csv_data(self, file_path: str, lottery_type: LotteryType = None) -> bool:
        """
        从CSV文件加载彩票数据

        Args:
            file_path: CSV文件路径
            lottery_type: 彩票类型，None表示使用当前类型

        Returns:
            bool: 加载是否成功
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")

            # 确定彩票类型
            if lottery_type:
                self.current_lottery_type = lottery_type

            # 获取彩票配置
            config = LotteryConfig.get_config(self.current_lottery_type)
            csv_columns = config.get('csv_columns', {})

            # 读取CSV数据
            self.data = pd.read_csv(file_path, encoding='utf-8')

            # 验证数据格式（根据彩票类型）
            if self.current_lottery_type == LotteryType.P5:
                # 排列五数据验证
                required_columns = [
                    csv_columns.get('date', 'date'),
                    csv_columns.get('period', 'period'),
                    csv_columns.get('numbers', 'numbers')
                ]
                if not all(col in self.data.columns for col in required_columns):
                    raise ValueError(f"排列五CSV文件格式不正确，缺少必要的列: {required_columns}")

                # 清理排列五数据
                original_count = len(self.data)
                self.data = self.data.dropna(subset=[csv_columns.get('numbers', 'numbers')])
                cleaned_count = len(self.data)
            else:
                # 大乐透/双色球数据验证
                required_columns = [csv_columns.get('issue', '期号'), csv_columns.get('date', '开奖日期')]
                required_columns.extend(csv_columns.get('red_balls', []))
                required_columns.extend(csv_columns.get('blue_balls', []))

                if not all(col in self.data.columns for col in required_columns):
                    raise ValueError(f"CSV文件格式不正确，缺少必要的列: {required_columns}")

                # 清理数据：移除包含NaN的行
                original_count = len(self.data)
                ball_columns = csv_columns.get('red_balls', []) + csv_columns.get('blue_balls', [])
                self.data = self.data.dropna(subset=ball_columns)
                cleaned_count = len(self.data)

            if cleaned_count < original_count:
                print(f"清理数据: 移除了 {original_count - cleaned_count} 行包含空值的记录")

            if cleaned_count == 0:
                raise ValueError("清理后没有有效数据")

            # 转换日期格式
            date_column = csv_columns.get('date', '开奖日期')
            self.data[date_column] = pd.to_datetime(self.data[date_column])

            # 按日期排序
            self.data = self.data.sort_values(date_column).reset_index(drop=True)

            print(f"成功加载{LotteryConfig.get_name(self.current_lottery_type)}数据: {len(self.data)} 条记录")
            return True

        except Exception as e:
            print(f"数据加载失败: {str(e)}")
            return False
    
    def get_red_balls(self, row_index: int) -> List[int]:
        """获取指定行的红球号码"""
        if self.data is None or row_index >= len(self.data):
            return []

        row = self.data.iloc[row_index]
        config = LotteryConfig.get_config(self.current_lottery_type)
        red_ball_columns = config.get('csv_columns', {}).get('red_balls', [])

        red_balls = []
        for col in red_ball_columns:
            try:
                value = row[col]
                # 检查是否为NaN或空值
                if pd.isna(value):
                    print(f"警告: 第{row_index+1}行的{col}列包含空值，跳过")
                    continue
                red_balls.append(int(float(value)))  # 先转float再转int，处理可能的小数
            except (ValueError, TypeError) as e:
                print(f"警告: 第{row_index+1}行的{col}列数据转换失败: {value}, 错误: {e}")
                continue

        return red_balls

    def get_blue_balls(self, row_index: int) -> List[int]:
        """获取指定行的蓝球号码"""
        if self.data is None or row_index >= len(self.data):
            return []

        row = self.data.iloc[row_index]
        config = LotteryConfig.get_config(self.current_lottery_type)
        blue_ball_columns = config.get('csv_columns', {}).get('blue_balls', [])

        blue_balls = []
        for col in blue_ball_columns:
            try:
                value = row[col]
                # 检查是否为NaN或空值
                if pd.isna(value):
                    print(f"警告: 第{row_index+1}行的{col}列包含空值，跳过")
                    continue
                blue_balls.append(int(float(value)))  # 先转float再转int，处理可能的小数
            except (ValueError, TypeError) as e:
                print(f"警告: 第{row_index+1}行的{col}列数据转换失败: {value}, 错误: {e}")
                continue

        return blue_balls
    
    def get_data_count(self) -> int:
        """获取数据总数"""
        return len(self.data) if self.data is not None else 0
    
    def get_raw_data(self) -> Optional[pd.DataFrame]:
        """获取原始数据"""
        return self.data
    
    def set_analyzed_data(self, analyzed_data: pd.DataFrame):
        """设置分析后的数据"""
        self.analyzed_data = analyzed_data
    
    def get_analyzed_data(self) -> Optional[pd.DataFrame]:
        """获取分析后的数据"""
        return self.analyzed_data
    
    def load_from_database(self, lottery_type: LotteryType = None) -> bool:
        """
        从数据库加载已分析的数据

        Args:
            lottery_type: 彩票类型，None表示使用当前类型

        Returns:
            bool: 加载是否成功
        """
        try:
            if lottery_type:
                self.current_lottery_type = lottery_type

            if self.db_manager.has_data(self.current_lottery_type):
                self.analyzed_data = self.db_manager.load_results(self.current_lottery_type)
                if self.analyzed_data is not None:
                    lottery_name = LotteryConfig.get_name(self.current_lottery_type)
                    print(f"从数据库加载了 {len(self.analyzed_data)} 条{lottery_name}分析结果")
                    return True

            lottery_name = LotteryConfig.get_name(self.current_lottery_type)
            print(f"数据库中没有{lottery_name}数据，需要重新分析")
            return False

        except Exception as e:
            print(f"从数据库加载数据失败: {e}")
            return False

    def save_to_database(self, analyzed_data: pd.DataFrame) -> bool:
        """
        保存分析结果到数据库

        Args:
            analyzed_data: 分析结果数据框

        Returns:
            bool: 保存是否成功
        """
        try:
            success = self.db_manager.save_results(analyzed_data, self.current_lottery_type)
            if success:
                self.analyzed_data = analyzed_data
                lottery_name = LotteryConfig.get_name(self.current_lottery_type)
                print(f"{lottery_name}分析结果已保存到数据库")
            return success

        except Exception as e:
            print(f"保存数据到数据库失败: {e}")
            return False

    def rebuild_database(self, analyzed_data: pd.DataFrame) -> bool:
        """
        重建数据库（用于导入新数据时）

        Args:
            analyzed_data: 新的分析结果数据框

        Returns:
            bool: 重建是否成功
        """
        try:
            success = self.db_manager.rebuild_database(analyzed_data, self.current_lottery_type)
            if success:
                self.analyzed_data = analyzed_data
                lottery_name = LotteryConfig.get_name(self.current_lottery_type)
                print(f"{lottery_name}数据库重建完成")
            return success

        except Exception as e:
            print(f"重建数据库失败: {e}")
            return False

    def get_database_statistics(self) -> dict:
        """获取当前彩票类型的数据库统计信息"""
        return self.db_manager.get_statistics(self.current_lottery_type)

    def has_database_data(self) -> bool:
        """检查当前彩票类型的数据库是否有数据"""
        return self.db_manager.has_data(self.current_lottery_type)

    def set_lottery_type(self, lottery_type: LotteryType):
        """设置当前彩票类型"""
        self.current_lottery_type = lottery_type

    def get_lottery_type(self) -> LotteryType:
        """获取当前彩票类型"""
        return self.current_lottery_type

    def clear_data(self):
        """清空数据"""
        self.data = None
        self.analyzed_data = None
