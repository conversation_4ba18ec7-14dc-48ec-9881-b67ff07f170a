"""
SQLite3数据库管理模块
"""
import sqlite3
import os
import pandas as pd
from typing import List, Dict, Optional, Tuple
from config.lottery_config import LotteryType


class SQLiteManager:
    """SQLite数据库管理器"""
    
    def __init__(self, db_path: str = "data/analysis_results.db"):
        """
        初始化数据库管理器

        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path

        # 确保数据库目录存在
        db_dir = os.path.dirname(db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir)

        self.conn = None
        # 初始化时创建/更新表结构
        self.connect()
    
    def get_connection(self):
        """获取数据库连接（线程安全）"""
        try:
            # 每次都创建新连接以确保线程安全
            conn = sqlite3.connect(self.db_path, check_same_thread=False)
            conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
            return conn
        except Exception as e:
            print(f"数据库连接失败: {e}")
            return None

    def connect(self):
        """连接数据库"""
        self.conn = self.get_connection()
        if self.conn:
            self.create_table()
            return True
        return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.conn:
            self.conn.close()
            self.conn = None
    
    def create_table(self):
        """创建分析结果表"""
        conn = self.get_connection()
        if not conn:
            return False

        try:
            # 检查表是否存在
            cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='analysis_results'")
            table_exists = cursor.fetchone() is not None

            if not table_exists:
                # 创建新表（支持所有彩票类型）
                sql = """
                CREATE TABLE analysis_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    lottery_type TEXT NOT NULL DEFAULT 'dlt',
                    date TEXT NOT NULL,
                    issue TEXT NOT NULL,
                    red_balls TEXT DEFAULT '',
                    blue_balls TEXT DEFAULT '',
                    odd_even_ratio TEXT NOT NULL,
                    odd_even_pattern TEXT NOT NULL,
                    last_odd_even_pattern TEXT NOT NULL,
                    region_ratio TEXT,
                    last_region_ratio TEXT,
                    last_zero_region_ratio TEXT,
                    -- 排列五专用字段
                    number1 INTEGER,
                    number2 INTEGER,
                    number3 INTEGER,
                    number4 INTEGER,
                    number5 INTEGER,
                    odd_even_code INTEGER,
                    big_small_ratio TEXT,
                    big_small_pattern TEXT,
                    big_small_code INTEGER,
                    consecutive_count INTEGER,
                    repeat_count INTEGER,
                    number_format TEXT,
                    format_colors TEXT,
                    position_intervals TEXT,
                    last_big_small_pattern TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """
                conn.execute(sql)
                conn.commit()
                print("创建新的分析结果表（支持排列五）")
            else:
                # 检查并添加缺失的列
                cursor = conn.execute("PRAGMA table_info(analysis_results)")
                columns = [row[1] for row in cursor.fetchall()]

                # 需要添加的新列
                new_columns = [
                    ('lottery_type', 'TEXT NOT NULL DEFAULT "dlt"'),
                    ('number1', 'INTEGER'),
                    ('number2', 'INTEGER'),
                    ('number3', 'INTEGER'),
                    ('number4', 'INTEGER'),
                    ('number5', 'INTEGER'),
                    ('odd_even_code', 'INTEGER'),
                    ('big_small_ratio', 'TEXT'),
                    ('big_small_pattern', 'TEXT'),
                    ('big_small_code', 'INTEGER'),
                    ('consecutive_count', 'INTEGER'),
                    ('repeat_count', 'INTEGER'),
                    ('number_format', 'TEXT'),
                    ('format_colors', 'TEXT'),
                    ('position_intervals', 'TEXT'),
                    ('last_big_small_pattern', 'TEXT')
                ]

                for col_name, col_type in new_columns:
                    if col_name not in columns:
                        conn.execute(f"ALTER TABLE analysis_results ADD COLUMN {col_name} {col_type}")
                        print(f"已添加{col_name}列到现有表")

                conn.commit()

            conn.close()
            return True
        except Exception as e:
            print(f"创建/更新表失败: {e}")
            if conn:
                conn.close()
            return False
    
    def has_data(self, lottery_type: LotteryType = None) -> bool:
        """检查数据库是否有数据"""
        conn = self.get_connection()
        if not conn:
            return False

        try:
            if lottery_type:
                cursor = conn.execute("SELECT COUNT(*) FROM analysis_results WHERE lottery_type = ?",
                                    (lottery_type.value,))
            else:
                cursor = conn.execute("SELECT COUNT(*) FROM analysis_results")
            count = cursor.fetchone()[0]
            conn.close()
            return count > 0
        except Exception as e:
            print(f"检查数据失败: {e}")
            if conn:
                conn.close()
            return False
    
    def load_results(self, lottery_type: LotteryType = None) -> Optional[pd.DataFrame]:
        """
        从数据库加载分析结果

        Args:
            lottery_type: 彩票类型，None表示加载所有类型

        Returns:
            pd.DataFrame: 分析结果数据框，失败时返回None
        """
        conn = self.get_connection()
        if not conn:
            return None

        try:
            if lottery_type:
                if lottery_type == LotteryType.P5:
                    # 排列五数据查询 - 按日期降序显示最新数据
                    sql = """
                    SELECT date, issue, number1, number2, number3, number4, number5,
                           odd_even_ratio, odd_even_pattern, odd_even_code,
                           last_odd_even_pattern, big_small_ratio, big_small_pattern,
                           big_small_code, consecutive_count, repeat_count,
                           number_format, format_colors, position_intervals, last_big_small_pattern
                    FROM analysis_results
                    WHERE lottery_type = ?
                    ORDER BY date DESC
                    """
                else:
                    # 大乐透/双色球数据查询 - 按日期降序显示最新数据
                    sql = """
                    SELECT date, issue, red_balls, blue_balls, odd_even_ratio,
                           odd_even_pattern, last_odd_even_pattern, region_ratio,
                           last_region_ratio, last_zero_region_ratio
                    FROM analysis_results
                    WHERE lottery_type = ?
                    ORDER BY date DESC
                    """
                df = pd.read_sql_query(sql, conn, params=(lottery_type.value,))
            else:
                # 加载所有类型 - 按日期降序显示最新数据
                sql = """
                SELECT date, issue, red_balls, blue_balls, odd_even_ratio,
                       odd_even_pattern, last_odd_even_pattern, region_ratio,
                       last_region_ratio, last_zero_region_ratio
                FROM analysis_results
                ORDER BY date DESC
                """
                df = pd.read_sql_query(sql, conn)

            conn.close()

            if len(df) > 0:
                # 根据彩票类型重命名列
                if lottery_type == LotteryType.P5:
                    # 排列五列名
                    df.columns = [
                        '日期', '期号', '号码1', '号码2', '号码3', '号码4', '号码5',
                        '奇偶比', '奇偶排布', '奇偶码', '上次奇偶排布',
                        '大小比', '大小排布', '大小码', '连号', '重号',
                        '号码格式', '格式颜色', '间隔', '上次大小排布'
                    ]

                    # 处理格式颜色数据
                    if '格式颜色' in df.columns:
                        df['格式颜色'] = df['格式颜色'].apply(
                            lambda x: x.split(',') if isinstance(x, str) and x else ['normal'] * 5
                        )
                else:
                    # 大乐透/双色球列名
                    df.columns = [
                        '日期', '期号', '红球', '蓝球', '奇偶比',
                        '奇偶排布', '上次奇偶排布', '分区比',
                        '上次分区比', '上次0分区比'
                    ]
                lottery_name = lottery_type.value if lottery_type else "所有类型"
                print(f"从数据库加载了 {len(df)} 条{lottery_name}记录")
                return df
            else:
                print("数据库中没有数据")
                return None

        except Exception as e:
            print(f"加载数据失败: {e}")
            if conn:
                conn.close()
            return None
    
    def save_results(self, data: pd.DataFrame, lottery_type: LotteryType) -> bool:
        """
        保存分析结果到数据库

        Args:
            data: 分析结果数据框
            lottery_type: 彩票类型

        Returns:
            bool: 保存是否成功
        """
        conn = self.get_connection()
        if not conn:
            return False

        try:
            # 根据彩票类型准备数据
            data_list = []

            if lottery_type == LotteryType.P5:
                # 排列五数据格式
                for _, row in data.iterrows():
                    data_list.append((
                        lottery_type.value,
                        str(row['日期']),
                        str(row['期号']),
                        '',  # red_balls (空字符串而不是None)
                        '',  # blue_balls (空字符串而不是None)
                        str(row['奇偶比']),
                        str(row['奇偶排布']),
                        str(row['上次奇偶排布']),
                        '',  # region_ratio (空字符串而不是None)
                        '',  # last_region_ratio (空字符串而不是None)
                        '',  # last_zero_region_ratio (空字符串而不是None)
                        int(row['号码1']),
                        int(row['号码2']),
                        int(row['号码3']),
                        int(row['号码4']),
                        int(row['号码5']),
                        int(row['奇偶码']),
                        str(row['大小比']),
                        str(row['大小排布']),
                        int(row['大小码']),
                        int(row['连号']),
                        int(row['重号']),
                        str(row['号码格式']),
                        ','.join(row['格式颜色']) if '格式颜色' in row and isinstance(row['格式颜色'], list) else 'normal,normal,normal,normal,normal',
                        str(row['间隔']),
                        str(row['上次大小排布'])
                    ))

                # 排列五插入SQL
                sql = """
                INSERT INTO analysis_results
                (lottery_type, date, issue, red_balls, blue_balls, odd_even_ratio,
                 odd_even_pattern, last_odd_even_pattern, region_ratio,
                 last_region_ratio, last_zero_region_ratio, number1, number2, number3,
                 number4, number5, odd_even_code, big_small_ratio, big_small_pattern,
                 big_small_code, consecutive_count, repeat_count, number_format,
                 format_colors, position_intervals, last_big_small_pattern)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
            else:
                # 大乐透/双色球数据格式
                for _, row in data.iterrows():
                    data_list.append((
                        lottery_type.value,
                        str(row['日期']),
                        str(row['期号']),
                        str(row['红球']),
                        str(row['蓝球']),
                        str(row['奇偶比']),
                        str(row['奇偶排布']),
                        str(row['上次奇偶排布']),
                        str(row['分区比']),
                        str(row['上次分区比']),
                        str(row['上次0分区比'])
                    ))

                # 大乐透/双色球插入SQL
                sql = """
                INSERT INTO analysis_results
                (lottery_type, date, issue, red_balls, blue_balls, odd_even_ratio,
                 odd_even_pattern, last_odd_even_pattern, region_ratio,
                 last_region_ratio, last_zero_region_ratio)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """

            conn.executemany(sql, data_list)
            conn.commit()
            conn.close()

            print(f"成功保存 {len(data_list)} 条{lottery_type.value}记录到数据库")
            return True

        except Exception as e:
            print(f"保存数据失败: {e}")
            if conn:
                conn.rollback()
                conn.close()
            return False
    
    def rebuild_database(self, new_data: pd.DataFrame, lottery_type: LotteryType) -> bool:
        """
        重建数据库（清空指定彩票类型的旧数据并保存新数据）

        Args:
            new_data: 新的分析结果数据框
            lottery_type: 彩票类型

        Returns:
            bool: 重建是否成功
        """
        conn = self.get_connection()
        if not conn:
            return False

        try:
            # 清空指定彩票类型的现有数据
            conn.execute("DELETE FROM analysis_results WHERE lottery_type = ?", (lottery_type.value,))
            conn.commit()
            conn.close()
            print(f"已清空数据库中的{lottery_type.value}旧数据")

            # 保存新数据
            success = self.save_results(new_data, lottery_type)

            if success:
                print(f"{lottery_type.value}数据库重建完成")

            return success

        except Exception as e:
            print(f"重建数据库失败: {e}")
            if conn:
                conn.rollback()
                conn.close()
            return False
    
    def get_statistics(self, lottery_type: LotteryType = None) -> Dict[str, any]:
        """
        获取数据库统计信息

        Args:
            lottery_type: 彩票类型，None表示获取所有类型的统计

        Returns:
            Dict: 统计信息
        """
        conn = self.get_connection()
        if not conn:
            return {
                'total_records': 0,
                'start_date': 'N/A',
                'end_date': 'N/A',
                'lottery_type': lottery_type.value if lottery_type else 'all'
            }

        try:
            if lottery_type:
                cursor = conn.execute("SELECT COUNT(*) FROM analysis_results WHERE lottery_type = ?",
                                    (lottery_type.value,))
                total_count = cursor.fetchone()[0]

                cursor = conn.execute("SELECT MIN(date), MAX(date) FROM analysis_results WHERE lottery_type = ?",
                                    (lottery_type.value,))
                date_range = cursor.fetchone()
            else:
                cursor = conn.execute("SELECT COUNT(*) FROM analysis_results")
                total_count = cursor.fetchone()[0]

                cursor = conn.execute("SELECT MIN(date), MAX(date) FROM analysis_results")
                date_range = cursor.fetchone()

            conn.close()

            return {
                'total_records': total_count,
                'start_date': date_range[0] if date_range[0] else 'N/A',
                'end_date': date_range[1] if date_range[1] else 'N/A',
                'lottery_type': lottery_type.value if lottery_type else 'all'
            }

        except Exception as e:
            print(f"获取统计信息失败: {e}")
            if conn:
                conn.close()
            return {
                'total_records': 0,
                'start_date': 'N/A',
                'end_date': 'N/A',
                'lottery_type': lottery_type.value if lottery_type else 'all'
            }
    
    def __del__(self):
        """析构函数，确保连接关闭"""
        self.disconnect()
