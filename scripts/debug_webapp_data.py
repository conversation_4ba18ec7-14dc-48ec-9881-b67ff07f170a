#!/usr/bin/env python3
"""
调试Web应用数据问题
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from config.lottery_config import LotteryType
from model.db_manager import SQLiteManager
from webapp.data_logic import format_record_for_web

def check_database_structure():
    """检查数据库结构"""
    print("=== 检查数据库结构 ===\n")
    
    db_manager = SQLiteManager()
    
    # 检查大乐透数据
    print("1. 大乐透数据检查:")
    dlt_data = db_manager.load_results(LotteryType.DLT)
    
    if dlt_data is not None and len(dlt_data) > 0:
        print(f"   记录数: {len(dlt_data)}")
        print(f"   列名: {list(dlt_data.columns)}")
        
        # 显示第一条记录
        first_record = dlt_data.iloc[0]
        print(f"   第一条记录:")
        for col, value in first_record.items():
            print(f"     {col}: {value}")
        
        # 测试格式化
        print(f"\n   测试格式化:")
        try:
            formatted = format_record_for_web(first_record, 'dlt')
            print(f"     格式化成功: {formatted}")
        except Exception as e:
            print(f"     格式化失败: {e}")
    else:
        print("   无大乐透数据")
    
    print("\n" + "="*50)
    
    # 检查排列五数据
    print("2. 排列五数据检查:")
    p5_data = db_manager.load_results(LotteryType.P5)
    
    if p5_data is not None and len(p5_data) > 0:
        print(f"   记录数: {len(p5_data)}")
        print(f"   列名: {list(p5_data.columns)}")
        
        # 显示第一条记录
        first_record = p5_data.iloc[0]
        print(f"   第一条记录:")
        for col, value in first_record.items():
            print(f"     {col}: {value}")
        
        # 测试格式化
        print(f"\n   测试格式化:")
        try:
            formatted = format_record_for_web(first_record, 'p5')
            print(f"     格式化成功: {formatted}")
        except Exception as e:
            print(f"     格式化失败: {e}")
    else:
        print("   无排列五数据")

def test_api_data():
    """测试API数据返回"""
    print("\n=== 测试API数据返回 ===\n")
    
    from webapp.data_logic import get_lottery_data
    
    # 测试大乐透数据
    print("1. 测试大乐透API:")
    try:
        dlt_result = get_lottery_data('dlt', 0, 5)
        print(f"   总记录数: {dlt_result['total']}")
        print(f"   过滤记录数: {dlt_result['filtered']}")
        print(f"   返回记录数: {len(dlt_result['records'])}")
        
        if dlt_result['records']:
            print(f"   第一条记录: {dlt_result['records'][0]}")
        else:
            print("   无记录返回")
    except Exception as e:
        print(f"   API调用失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "="*50)
    
    # 测试排列五数据
    print("2. 测试排列五API:")
    try:
        p5_result = get_lottery_data('p5', 0, 5)
        print(f"   总记录数: {p5_result['total']}")
        print(f"   过滤记录数: {p5_result['filtered']}")
        print(f"   返回记录数: {len(p5_result['records'])}")
        
        if p5_result['records']:
            print(f"   第一条记录: {p5_result['records'][0]}")
        else:
            print("   无记录返回")
    except Exception as e:
        print(f"   API调用失败: {e}")
        import traceback
        traceback.print_exc()

def check_column_mapping():
    """检查列名映射"""
    print("\n=== 检查列名映射 ===\n")
    
    db_manager = SQLiteManager()
    
    # 直接查询数据库
    conn = db_manager.get_connection()
    if conn:
        try:
            # 查询大乐透表结构
            cursor = conn.execute("PRAGMA table_info(analysis_results)")
            columns = cursor.fetchall()
            
            print("数据库表结构:")
            for col in columns:
                print(f"  {col[1]} ({col[2]})")
            
            # 查询大乐透数据样例
            cursor = conn.execute("""
                SELECT * FROM analysis_results 
                WHERE lottery_type = 'dlt' 
                LIMIT 1
            """)
            
            row = cursor.fetchone()
            if row:
                print(f"\n大乐透数据样例:")
                for i, value in enumerate(row):
                    col_name = columns[i][1] if i < len(columns) else f"col_{i}"
                    print(f"  {col_name}: {value}")
            else:
                print("\n无大乐透数据")
                
        except Exception as e:
            print(f"查询失败: {e}")
        finally:
            conn.close()

def main():
    """主函数"""
    print("Web应用数据调试工具")
    print("="*50)
    
    # 检查数据库结构
    check_database_structure()
    
    # 检查列名映射
    check_column_mapping()
    
    # 测试API数据
    test_api_data()
    
    print("\n" + "="*50)
    print("调试完成")

if __name__ == "__main__":
    main()
