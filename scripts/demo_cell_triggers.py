#!/usr/bin/env python3
"""
演示不同的单元格属性触发方式
"""
import sys
import os
import tkinter as tk
from tkinter import ttk

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from view.enhanced_colored_treeview import EnhancedColoredTreeview, NumberFormatColorizer
from view.cell_colored_table import P5ColoredTable
from analysis.p5_analysis import calculate_number_format_with_color
from view.colored_treeview import NumberFormatDisplay


class CellTriggerDemo:
    """单元格触发演示"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("单元格属性触发方式演示")
        self.root.geometry("1200x800")
        
        self.create_widgets()
        self.load_sample_data()
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建笔记本控件
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 方式1：基于文本标记的Treeview
        self.create_text_marker_tab(notebook)
        
        # 方式2：基于行标签的Treeview
        self.create_row_tag_tab(notebook)
        
        # 方式3：基于规则的增强Treeview
        self.create_rule_based_tab(notebook)
        
        # 方式4：真彩色Canvas表格
        self.create_canvas_table_tab(notebook)
        
        # 方式5：事件触发的动态颜色
        self.create_event_trigger_tab(notebook)
    
    def create_text_marker_tab(self, notebook):
        """方式1：基于文本标记"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="文本标记方式")
        
        # 说明
        info_label = tk.Label(frame, text="方式1：通过修改显示文本来标记重复号码", 
                             font=('Arial', 12, 'bold'), fg='blue')
        info_label.pack(pady=10)
        
        # 创建Treeview
        columns = ('期号', '号码', '格式', '彩色格式', '说明')
        tree = ttk.Treeview(frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120, anchor='center')
        
        tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 添加示例数据
        sample_data = [
            ('25001', '11234', 'AABCD', '[A][A]BCD', '第1、2位重复'),
            ('25002', '12335', 'ABCCD', 'AB[C][C]D', '第3、4位重复'),
            ('25003', '12145', 'ABACD', '[A]B[A]CD', '第1、3位重复'),
            ('25004', '55567', 'AAABC', '[A][A][A]BC', '第1、2、3位重复'),
            ('25005', '12345', 'ABCDE', 'ABCDE', '无重复'),
        ]
        
        for data in sample_data:
            tree.insert('', tk.END, values=data)
        
        # 说明文本
        desc_text = """
特点：
• 简单易实现，兼容性好
• 通过[方括号]标记重复字符
• 不需要特殊组件，使用标准Treeview
• 适合文本输出和简单显示

缺点：
• 只能通过文本符号区分，不是真正的颜色
• 显示效果有限
        """
        
        desc_label = tk.Label(frame, text=desc_text, justify=tk.LEFT, 
                             font=('Arial', 9), bg='#f8f8f8', padx=10, pady=10)
        desc_label.pack(fill=tk.X, padx=10, pady=5)
    
    def create_row_tag_tab(self, notebook):
        """方式2：基于行标签"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="行标签方式")
        
        # 说明
        info_label = tk.Label(frame, text="方式2：使用Treeview行标签设置整行颜色", 
                             font=('Arial', 12, 'bold'), fg='blue')
        info_label.pack(pady=10)
        
        # 创建Treeview
        columns = ('期号', '号码1', '号码2', '号码3', '号码4', '号码5', '格式', '类型')
        tree = ttk.Treeview(frame, columns=columns, show='headings', height=10)
        
        # 配置行标签
        tree.tag_configure('repeat_row', background='lightblue', foreground='darkblue')
        tree.tag_configure('normal_row', background='white', foreground='black')
        tree.tag_configure('highlight_row', background='yellow', foreground='black')
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=60, anchor='center')
        
        tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 添加示例数据
        sample_data = [
            (('25001', 1, 1, 2, 3, 4, 'AABCD', '重复行'), 'repeat_row'),
            (('25002', 2, 4, 6, 8, 0, 'ABCDE', '正常行'), 'normal_row'),
            (('25003', 2, 5, 3, 7, 3, 'ABCDC', '重复行'), 'repeat_row'),
            (('25004', 1, 2, 3, 4, 5, 'ABCDE', '正常行'), 'normal_row'),
            (('25005', 4, 4, 4, 5, 6, 'AAABC', '重复行'), 'repeat_row'),
        ]
        
        for data, tag in sample_data:
            tree.insert('', tk.END, values=data, tags=(tag,))
        
        # 添加交互功能
        def on_double_click(event):
            item = tree.selection()[0]
            current_tags = tree.item(item, 'tags')
            if 'highlight_row' in current_tags:
                new_tag = 'repeat_row' if '重复' in tree.item(item, 'values')[7] else 'normal_row'
            else:
                new_tag = 'highlight_row'
            tree.item(item, tags=(new_tag,))
        
        tree.bind('<Double-1>', on_double_click)
        
        # 说明文本
        desc_text = """
特点：
• 可以设置整行的背景色和前景色
• 支持多种预定义标签
• 双击可以切换高亮状态
• 适合按行分类显示

缺点：
• 只能设置整行颜色，不能单独设置单元格
• 颜色种类有限
        """
        
        desc_label = tk.Label(frame, text=desc_text, justify=tk.LEFT, 
                             font=('Arial', 9), bg='#f8f8f8', padx=10, pady=10)
        desc_label.pack(fill=tk.X, padx=10, pady=5)
    
    def create_rule_based_tab(self, notebook):
        """方式3：基于规则的增强Treeview"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="规则引擎方式")
        
        # 说明
        info_label = tk.Label(frame, text="方式3：使用规则引擎自动应用颜色标记", 
                             font=('Arial', 12, 'bold'), fg='blue')
        info_label.pack(pady=10)
        
        # 创建增强Treeview
        columns = ('期号', '号码1', '号码2', '号码3', '号码4', '号码5', '格式', '奇偶比')
        tree = EnhancedColoredTreeview(frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=70, anchor='center')
        
        # 添加颜色规则
        colorizer = NumberFormatColorizer()
        
        # 为号码列添加奇偶规则
        for i in range(1, 6):
            tree.add_color_rule(f'号码{i}', colorizer.create_odd_even_rule(), f'odd_even_{i}')
        
        # 为格式列添加重复规则
        tree.add_color_rule('格式', colorizer.create_repeat_rule(), 'repeat_format')
        
        tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 添加示例数据
        sample_data = [
            ('25001', 1, 1, 2, 3, 4, '[A][A]BCD', '3:2'),
            ('25002', 2, 4, 6, 8, 0, 'ABCDE', '0:5'),
            ('25003', 1, 3, 5, 7, 9, 'ABCDE', '5:0'),
            ('25004', 2, 5, 3, 7, 3, 'AB[C]D[C]', '3:2'),
            ('25005', 4, 4, 4, 5, 6, '[A][A][A]BC', '1:4'),
        ]
        
        for data in sample_data:
            tree.insert_with_colors('', tk.END, values=data)
        
        # 说明文本
        desc_text = """
特点：
• 自动应用颜色规则
• 支持多种规则组合（奇偶、大小、重复等）
• 可以动态添加和修改规则
• 通过文本标记模拟颜色效果

优势：
• 灵活的规则系统
• 易于扩展和维护
• 支持复杂的颜色逻辑
        """
        
        desc_label = tk.Label(frame, text=desc_text, justify=tk.LEFT, 
                             font=('Arial', 9), bg='#f8f8f8', padx=10, pady=10)
        desc_label.pack(fill=tk.X, padx=10, pady=5)
    
    def create_canvas_table_tab(self, notebook):
        """方式4：真彩色Canvas表格"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Canvas真彩色")
        
        # 说明
        info_label = tk.Label(frame, text="方式4：基于Canvas的真正单元格级别彩色显示", 
                             font=('Arial', 12, 'bold'), fg='blue')
        info_label.pack(pady=10)
        
        # 创建Canvas表格
        table = P5ColoredTable(frame)
        table.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 添加示例数据
        sample_data = [
            ('25001', [1, 1, 2, 3, 4], 'AABCD', ['repeat', 'repeat', 'normal', 'normal', 'normal'], '3:2', '2:3'),
            ('25002', [2, 4, 6, 8, 0], 'ABCDE', ['normal', 'normal', 'normal', 'normal', 'normal'], '0:5', '2:3'),
            ('25003', [1, 3, 5, 7, 9], 'ABCDE', ['normal', 'normal', 'normal', 'normal', 'normal'], '5:0', '0:5'),
            ('25004', [2, 5, 3, 7, 3], 'ABCDC', ['normal', 'normal', 'repeat', 'normal', 'repeat'], '3:2', '3:2'),
            ('25005', [4, 4, 4, 5, 6], 'AAABC', ['repeat', 'repeat', 'repeat', 'normal', 'normal'], '1:4', '3:2'),
        ]
        
        for issue, numbers, format_chars, color_info, odd_even, big_small in sample_data:
            table.add_p5_record(issue, numbers, format_chars, color_info, odd_even, big_small)
        
        # 说明文本
        desc_text = """
特点：
• 真正的单元格级别颜色显示
• 支持任意背景色和文字色
• 可以点击单元格切换颜色
• 支持滚动和缩放

优势：
• 最佳的视觉效果
• 完全自定义的颜色控制
• 支持复杂的交互逻辑
• 可以实现各种特效

缺点：
• 实现复杂度较高
• 性能开销相对较大
        """
        
        desc_label = tk.Label(frame, text=desc_text, justify=tk.LEFT, 
                             font=('Arial', 9), bg='#f8f8f8', padx=10, pady=10)
        desc_label.pack(fill=tk.X, padx=10, pady=5)
    
    def create_event_trigger_tab(self, notebook):
        """方式5：事件触发的动态颜色"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="事件触发方式")
        
        # 说明
        info_label = tk.Label(frame, text="方式5：通过事件触发动态改变颜色显示", 
                             font=('Arial', 12, 'bold'), fg='blue')
        info_label.pack(pady=10)
        
        # 创建Treeview
        columns = ('期号', '号码1', '号码2', '号码3', '号码4', '号码5', '格式', '状态')
        tree = ttk.Treeview(frame, columns=columns, show='headings', height=8)
        
        # 配置多种标签
        tree.tag_configure('normal', background='white', foreground='black')
        tree.tag_configure('hover', background='lightgray', foreground='black')
        tree.tag_configure('selected', background='lightblue', foreground='darkblue')
        tree.tag_configure('repeat', background='lightcoral', foreground='darkred')
        tree.tag_configure('highlight', background='yellow', foreground='black')
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=70, anchor='center')
        
        tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 添加示例数据
        sample_data = [
            ('25001', 1, 1, 2, 3, 4, 'AABCD', '重复'),
            ('25002', 2, 4, 6, 8, 0, 'ABCDE', '正常'),
            ('25003', 1, 3, 5, 7, 9, 'ABCDE', '正常'),
            ('25004', 2, 5, 3, 7, 3, 'ABCDC', '重复'),
            ('25005', 4, 4, 4, 5, 6, 'AAABC', '重复'),
        ]
        
        items = []
        for data in sample_data:
            tag = 'repeat' if data[7] == '重复' else 'normal'
            item = tree.insert('', tk.END, values=data, tags=(tag,))
            items.append(item)
        
        # 事件处理
        def on_motion(event):
            item = tree.identify('item', event.x, event.y)
            for i in items:
                current_tags = tree.item(i, 'tags')
                if i == item and 'selected' not in current_tags and 'highlight' not in current_tags:
                    tree.item(i, tags=('hover',))
                elif 'hover' in current_tags:
                    original_tag = 'repeat' if '重复' in tree.item(i, 'values')[7] else 'normal'
                    tree.item(i, tags=(original_tag,))
        
        def on_click(event):
            item = tree.identify('item', event.x, event.y)
            if item:
                current_tags = tree.item(item, 'tags')
                if 'selected' in current_tags:
                    original_tag = 'repeat' if '重复' in tree.item(item, 'values')[7] else 'normal'
                    tree.item(item, tags=(original_tag,))
                else:
                    tree.item(item, tags=('selected',))
        
        def on_double_click(event):
            item = tree.identify('item', event.x, event.y)
            if item:
                tree.item(item, tags=('highlight',))
        
        tree.bind('<Motion>', on_motion)
        tree.bind('<Button-1>', on_click)
        tree.bind('<Double-1>', on_double_click)
        
        # 控制按钮
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        def reset_colors():
            for item in items:
                values = tree.item(item, 'values')
                tag = 'repeat' if values[7] == '重复' else 'normal'
                tree.item(item, tags=(tag,))
        
        ttk.Button(button_frame, text="重置颜色", command=reset_colors).pack(side=tk.LEFT, padx=5)
        
        # 说明文本
        desc_text = """
特点：
• 响应鼠标事件动态改变颜色
• 支持悬停、点击、双击等多种触发方式
• 可以实现复杂的交互逻辑
• 实时反馈用户操作

交互说明：
• 鼠标悬停：显示灰色背景
• 单击：切换选中状态（蓝色背景）
• 双击：高亮显示（黄色背景）
• 重置按钮：恢复原始颜色
        """
        
        desc_label = tk.Label(frame, text=desc_text, justify=tk.LEFT, 
                             font=('Arial', 9), bg='#f8f8f8', padx=10, pady=10)
        desc_label.pack(fill=tk.X, padx=10, pady=5)
    
    def load_sample_data(self):
        """加载示例数据"""
        pass
    
    def run(self):
        """运行演示"""
        self.root.mainloop()


def main():
    """主函数"""
    try:
        print("启动单元格属性触发方式演示...")
        demo = CellTriggerDemo()
        demo.run()
    except Exception as e:
        print(f"❌ 演示运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
