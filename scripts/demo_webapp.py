#!/usr/bin/env python3
"""
Web应用演示脚本
展示如何启动和使用彩票数据分析Web应用
"""
import os
import sys
import time
import webbrowser
import subprocess
import threading

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def check_dependencies():
    """检查依赖包"""
    print("🔍 检查依赖包...")
    
    required_packages = [
        'flask',
        'pandas',
        'numpy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def prepare_sample_data():
    """准备示例数据"""
    print("\n📊 准备示例数据...")
    
    try:
        from controller.main_controller import MainController
        from config.lottery_config import LotteryType
        from model.db_manager import SQLiteManager
        
        # 检查是否已有数据
        db_manager = SQLiteManager()
        
        # 检查排列五数据
        p5_data = db_manager.load_results(LotteryType.P5)
        if p5_data is not None and len(p5_data) > 0:
            print(f"  ✅ 排列五数据: {len(p5_data)} 条记录")
        else:
            print("  ⚠️  排列五数据为空，建议上传CSV文件")
        
        # 检查大乐透数据
        dlt_data = db_manager.load_results(LotteryType.DLT)
        if dlt_data is not None and len(dlt_data) > 0:
            print(f"  ✅ 大乐透数据: {len(dlt_data)} 条记录")
        else:
            print("  ⚠️  大乐透数据为空，建议上传CSV文件")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 数据检查失败: {e}")
        return False

def start_webapp():
    """启动Web应用"""
    print("\n🚀 启动Web应用...")
    
    try:
        # 导入Flask应用
        from webapp.app import app
        from webapp.data_logic import init_database
        
        # 初始化数据库
        if not init_database():
            print("❌ 数据库初始化失败")
            return None
        
        print("✅ 数据库初始化成功")
        
        # 在新线程中启动Flask应用
        def run_app():
            app.run(debug=False, host='127.0.0.1', port=5000, use_reloader=False)
        
        app_thread = threading.Thread(target=run_app, daemon=True)
        app_thread.start()
        
        # 等待应用启动
        time.sleep(2)
        
        return app_thread
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return None

def open_browser():
    """打开浏览器"""
    print("\n🌐 打开浏览器...")
    
    url = "http://127.0.0.1:5000"
    
    try:
        webbrowser.open(url)
        print(f"✅ 浏览器已打开: {url}")
        return True
    except Exception as e:
        print(f"❌ 打开浏览器失败: {e}")
        print(f"请手动在浏览器中访问: {url}")
        return False

def show_demo_guide():
    """显示演示指南"""
    print("\n" + "="*60)
    print("🎯 彩票数据分析Web应用演示指南")
    print("="*60)
    
    print("\n📋 演示步骤:")
    print("1. 🏠 首页 - 查看系统概览")
    print("2. 📊 数据分析 - 浏览历史开奖数据")
    print("   • 大乐透分析")
    print("   • 双色球分析") 
    print("   • 排列五分析")
    print("3. 🧮 实时分析 - 输入号码进行即时分析")
    print("4. 📁 数据上传 - 上传CSV文件")
    print("5. ℹ️  关于页面 - 了解系统功能")
    
    print("\n🎨 界面特色:")
    print("• 🔴 红色圆球 - 普通红球号码")
    print("• 🔵 蓝色圆球 - 蓝球号码")
    print("• 🟡 黄色圆球 - 重复号码高亮")
    print("• 📱 响应式设计 - 适配各种设备")
    
    print("\n🧪 测试建议:")
    print("• 在实时分析中输入: 1,2,3,4,5 (排列五)")
    print("• 在实时分析中输入: 01,12,23,28,35 和 03,11 (大乐透)")
    print("• 尝试上传示例CSV文件")
    print("• 测试表格的排序、搜索、分页功能")
    
    print("\n⚠️  注意事项:")
    print("• 首次使用需要上传历史数据")
    print("• 分析结果仅供参考")
    print("• 请理性购彩")

def create_sample_csv():
    """创建示例CSV文件"""
    print("\n📝 创建示例CSV文件...")
    
    # 创建示例目录
    sample_dir = os.path.join(project_root, 'webapp', 'samples')
    os.makedirs(sample_dir, exist_ok=True)
    
    # 大乐透示例
    dlt_sample = """日期,期号,红球1,红球2,红球3,红球4,红球5,蓝球1,蓝球2
2024-01-01,24001,01,12,23,28,35,03,11
2024-01-03,24002,05,15,18,25,33,02,09
2024-01-06,24003,02,08,19,26,34,01,12
2024-01-08,24004,07,14,21,29,35,04,10
2024-01-10,24005,03,11,17,24,31,05,08"""
    
    with open(os.path.join(sample_dir, 'dlt_sample.csv'), 'w', encoding='utf-8') as f:
        f.write(dlt_sample)
    
    # 双色球示例
    ssq_sample = """日期,期号,红球1,红球2,红球3,红球4,红球5,红球6,蓝球
2024-01-02,24001,01,05,12,18,25,33,08
2024-01-04,24002,03,09,15,21,28,31,12
2024-01-07,24003,02,07,14,20,27,32,05
2024-01-09,24004,04,10,16,22,29,33,15
2024-01-11,24005,06,11,17,23,30,31,03"""
    
    with open(os.path.join(sample_dir, 'ssq_sample.csv'), 'w', encoding='utf-8') as f:
        f.write(ssq_sample)
    
    # 排列五示例
    p5_sample = """日期,期号,号码1,号码2,号码3,号码4,号码5
2024-01-01,24001,1,2,3,4,5
2024-01-02,24002,6,7,8,9,0
2024-01-03,24003,2,5,3,7,3
2024-01-04,24004,4,4,4,5,6
2024-01-05,24005,1,1,2,3,4"""
    
    with open(os.path.join(sample_dir, 'p5_sample.csv'), 'w', encoding='utf-8') as f:
        f.write(p5_sample)
    
    print(f"✅ 示例CSV文件已创建在: {sample_dir}")
    print("  • dlt_sample.csv - 大乐透示例")
    print("  • ssq_sample.csv - 双色球示例")
    print("  • p5_sample.csv - 排列五示例")

def main():
    """主函数"""
    print("🎯 彩票数据分析Web应用演示")
    print("="*50)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 准备数据
    prepare_sample_data()
    
    # 创建示例文件
    create_sample_csv()
    
    # 启动Web应用
    app_thread = start_webapp()
    if not app_thread:
        return
    
    # 打开浏览器
    open_browser()
    
    # 显示演示指南
    show_demo_guide()
    
    print("\n" + "="*60)
    print("🎉 Web应用已启动！")
    print("🔗 访问地址: http://127.0.0.1:5000")
    print("📱 支持手机、平板、电脑访问")
    print("⌨️  按 Ctrl+C 停止演示")
    print("="*60)
    
    try:
        # 保持程序运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n\n👋 演示结束，感谢使用！")
        print("💡 您可以通过以下方式再次启动:")
        print("   python webapp/run.py")
        print("   或")
        print("   python scripts/demo_webapp.py")

if __name__ == "__main__":
    main()
