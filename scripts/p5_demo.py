#!/usr/bin/env python3
"""
排列五数据分析演示脚本
展示排列五彩票数据分析功能的完整流程
"""
import sys
import os
import pandas as pd

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from config.lottery_config import LotteryType, LotteryConfig
from model.data_model import LotteryDataModel
from model.db_manager import SQLiteManager
from analysis.analyzer import LotteryAnalyzer
from controller.main_controller import MainController


def display_p5_config():
    """显示排列五配置信息"""
    print("="*60)
    print("排列五彩票配置信息")
    print("="*60)
    
    config = LotteryConfig.get_config(LotteryType.P5)
    print(f"彩票名称: {config['name']}")
    print(f"号码范围: {config['number_range'][0]}-{config['number_range'][1]}")
    print(f"位置数量: {config['position_count']}")
    print(f"大小数分界线: {config['big_small_threshold']} (≥{config['big_small_threshold']}为大数)")
    print(f"数据文件: {config['csv_file']}")
    
    features = config['analysis_features']
    print("\n支持的分析特征:")
    for feature, enabled in features.items():
        status = "✓" if enabled else "✗"
        feature_names = {
            'odd_even': '奇偶分析',
            'big_small': '大小分析', 
            'consecutive': '连号分析',
            'repeat': '重号分析',
            'pattern': '号码格式分析'
        }
        print(f"  {status} {feature_names.get(feature, feature)}")


def load_and_analyze_p5_data():
    """加载并分析排列五数据"""
    print("\n" + "="*60)
    print("排列五数据加载与分析")
    print("="*60)
    
    # 创建主控制器
    controller = MainController()
    
    # 切换到排列五类型
    controller.set_lottery_type(LotteryType.P5)
    print(f"当前彩票类型: {LotteryConfig.get_name(LotteryType.P5)}")
    
    # 加载数据
    csv_file = LotteryConfig.get_csv_file(LotteryType.P5)
    print(f"\n正在加载数据文件: {csv_file}")
    
    success = controller.load_data(csv_file, LotteryType.P5)
    if not success:
        print("❌ 数据加载失败")
        return None
    
    # 分析数据
    print("正在进行数据分析...")
    analyzed_data = controller.analyze_data()
    
    if analyzed_data is None:
        print("❌ 数据分析失败")
        return None
    
    print(f"✓ 成功分析 {len(analyzed_data)} 条记录")
    return analyzed_data


def display_analysis_summary(data):
    """显示分析结果摘要"""
    print("\n" + "="*60)
    print("排列五分析结果摘要")
    print("="*60)
    
    if data is None or len(data) == 0:
        print("无数据可显示")
        return
    
    # 基本统计
    print(f"数据期间: {data['日期'].min()} 至 {data['日期'].max()}")
    print(f"总期数: {len(data)}")
    
    # 奇偶比统计
    print("\n奇偶比分布:")
    odd_even_counts = data['奇偶比'].value_counts().sort_index()
    for ratio, count in odd_even_counts.items():
        percentage = count / len(data) * 100
        print(f"  {ratio}: {count}期 ({percentage:.1f}%)")
    
    # 大小比统计
    print("\n大小比分布:")
    big_small_counts = data['大小比'].value_counts().sort_index()
    for ratio, count in big_small_counts.items():
        percentage = count / len(data) * 100
        print(f"  {ratio}: {count}期 ({percentage:.1f}%)")
    
    # 连号统计
    print("\n连号分布:")
    consecutive_counts = data['连号'].value_counts().sort_index()
    for count, freq in consecutive_counts.items():
        percentage = freq / len(data) * 100
        print(f"  {count}连号: {freq}期 ({percentage:.1f}%)")
    
    # 重号统计
    print("\n重号分布:")
    repeat_counts = data['重号'].value_counts().sort_index()
    for count, freq in repeat_counts.items():
        percentage = freq / len(data) * 100
        print(f"  {count}重号: {freq}期 ({percentage:.1f}%)")


def display_recent_records(data, num_records=10):
    """显示最近的记录"""
    print(f"\n" + "="*60)
    print(f"最近 {num_records} 期开奖记录")
    print("="*60)
    
    if data is None or len(data) == 0:
        print("无数据可显示")
        return
    
    # 获取最近的记录
    recent_data = data.tail(num_records)
    
    print(f"{'期号':<10} {'日期':<12} {'号码':<15} {'奇偶比':<8} {'大小比':<8} {'连号':<4} {'重号':<4}")
    print("-" * 70)
    
    for _, row in recent_data.iterrows():
        numbers = f"{row['号码1']}{row['号码2']}{row['号码3']}{row['号码4']}{row['号码5']}"
        print(f"{row['期号']:<10} {row['日期']:<12} {numbers:<15} {row['奇偶比']:<8} {row['大小比']:<8} {row['连号']:<4} {row['重号']:<4}")


def analyze_patterns(data):
    """分析号码模式"""
    print("\n" + "="*60)
    print("号码模式分析")
    print("="*60)
    
    if data is None or len(data) == 0:
        print("无数据可分析")
        return
    
    # 奇偶排布模式统计
    print("最常见的奇偶排布模式:")
    odd_even_patterns = data['奇偶排布'].value_counts().head(10)
    for pattern, count in odd_even_patterns.items():
        percentage = count / len(data) * 100
        print(f"  {pattern}: {count}期 ({percentage:.1f}%)")
    
    # 大小排布模式统计
    print("\n最常见的大小排布模式:")
    big_small_patterns = data['大小排布'].value_counts().head(10)
    for pattern, count in big_small_patterns.items():
        percentage = count / len(data) * 100
        print(f"  {pattern}: {count}期 ({percentage:.1f}%)")
    
    # 号码格式统计
    print("\n最常见的号码格式:")
    number_formats = data['号码格式'].value_counts().head(10)
    for format_str, count in number_formats.items():
        percentage = count / len(data) * 100
        print(f"  {format_str}: {count}期 ({percentage:.1f}%)")


def demonstrate_database_operations():
    """演示数据库操作"""
    print("\n" + "="*60)
    print("数据库操作演示")
    print("="*60)
    
    # 创建数据库管理器
    db_manager = SQLiteManager()
    
    # 检查数据库统计
    stats = {}
    for lottery_type in [LotteryType.DLT, LotteryType.SSQ, LotteryType.P5]:
        has_data = db_manager.has_data(lottery_type)
        if has_data:
            data = db_manager.load_results(lottery_type)
            stats[lottery_type.value] = len(data) if data is not None else 0
        else:
            stats[lottery_type.value] = 0
    
    print("数据库中各彩票类型的记录数:")
    for lottery_type, count in stats.items():
        type_name = LotteryConfig.get_name(LotteryType(lottery_type))
        print(f"  {type_name}: {count} 条记录")


def main():
    """主函数"""
    print("🎯 排列五彩票数据分析系统演示")
    print("基于现有大乐透预测系统架构扩展")
    
    try:
        # 1. 显示配置信息
        display_p5_config()
        
        # 2. 加载和分析数据
        analyzed_data = load_and_analyze_p5_data()
        
        if analyzed_data is not None:
            # 3. 显示分析摘要
            display_analysis_summary(analyzed_data)
            
            # 4. 显示最近记录
            display_recent_records(analyzed_data)
            
            # 5. 分析模式
            analyze_patterns(analyzed_data)
        
        # 6. 演示数据库操作
        demonstrate_database_operations()
        
        print("\n" + "="*60)
        print("🎉 排列五数据分析演示完成！")
        print("系统已成功扩展支持排列五彩票数据分析")
        print("="*60)
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
