#!/usr/bin/env python3
"""
Miniconda环境设置脚本 - 专为Python 3.13优化
"""
import sys
import subprocess
import os


def check_conda_environment():
    """检查Conda环境"""
    print("=" * 60)
    print("检查Conda环境")
    print("=" * 60)
    
    # 检查是否在conda环境中
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env:
        print(f"✓ 当前Conda环境: {conda_env}")
    else:
        print("⚠ 未检测到Conda环境，建议激活conda环境")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"✓ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version >= (3, 13):
        print("✓ Python版本符合要求 (3.13+)")
        return True
    else:
        print("✗ Python版本过低，需要3.13+")
        return False


def install_with_conda():
    """使用conda安装依赖包"""
    print("\n" + "=" * 60)
    print("使用Conda安装依赖包")
    print("=" * 60)
    
    packages = [
        "pandas",
        "numpy", 
        "python-dateutil"
    ]
    
    for package in packages:
        print(f"\n正在安装 {package}...")
        try:
            result = subprocess.run([
                "conda", "install", "-y", package
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✓ {package} 安装成功")
            else:
                print(f"⚠ {package} 安装可能有问题: {result.stderr}")
        except FileNotFoundError:
            print("✗ 未找到conda命令，请确保Miniconda已正确安装")
            return False
    
    return True


def install_pyqt():
    """安装PyQt5"""
    print("\n" + "=" * 60)
    print("安装PyQt5")
    print("=" * 60)
    
    # 首先尝试conda安装
    print("尝试使用conda安装PyQt5...")
    try:
        result = subprocess.run([
            "conda", "install", "-y", "-c", "conda-forge", "pyqt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ PyQt5通过conda安装成功")
            return True
        else:
            print("⚠ conda安装PyQt5失败，尝试pip安装...")
    except FileNotFoundError:
        print("⚠ 未找到conda命令，尝试pip安装...")
    
    # 如果conda失败，尝试pip
    print("使用pip安装PyQt5...")
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "PyQt5"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ PyQt5通过pip安装成功")
            return True
        else:
            print(f"✗ PyQt5安装失败: {result.stderr}")
            print("\n建议尝试安装PySide6作为替代:")
            print("conda install -c conda-forge pyside6")
            return False
    except Exception as e:
        print(f"✗ 安装过程出错: {e}")
        return False


def test_imports():
    """测试导入"""
    print("\n" + "=" * 60)
    print("测试依赖包导入")
    print("=" * 60)
    
    test_packages = [
        ("pandas", "import pandas as pd"),
        ("numpy", "import numpy as np"),
        ("python-dateutil", "from datetime import datetime; import dateutil"),
        ("PyQt5", "from PyQt5.QtWidgets import QApplication")
    ]
    
    all_success = True
    
    for package_name, import_code in test_packages:
        try:
            exec(import_code)
            print(f"✓ {package_name} 导入成功")
        except ImportError as e:
            print(f"✗ {package_name} 导入失败: {e}")
            all_success = False
        except Exception as e:
            print(f"⚠ {package_name} 导入时出现问题: {e}")
            all_success = False
    
    return all_success


def create_conda_environment_file():
    """创建conda环境文件"""
    print("\n" + "=" * 60)
    print("创建Conda环境配置文件")
    print("=" * 60)
    
    env_content = """name: dlt-analysis
channels:
  - conda-forge
  - defaults
dependencies:
  - python=3.13
  - pandas>=2.0.0
  - numpy>=1.24.0
  - python-dateutil>=2.8.0
  - pyqt>=5.15.0
  - pip
  - pip:
    - # 如果需要其他pip包可以在这里添加
"""
    
    try:
        with open("environment.yml", "w", encoding="utf-8") as f:
            f.write(env_content)
        print("✓ 创建environment.yml文件成功")
        print("\n您可以使用以下命令创建新的conda环境:")
        print("conda env create -f environment.yml")
        print("conda activate dlt-analysis")
        return True
    except Exception as e:
        print(f"✗ 创建environment.yml失败: {e}")
        return False


def main():
    """主函数"""
    print("大乐透数据分析系统 - Miniconda环境设置")
    print("适用于Python 3.13")
    
    # 检查环境
    if not check_conda_environment():
        print("\n请升级Python到3.13+版本")
        return
    
    # 创建conda环境文件
    create_conda_environment_file()
    
    # 安装依赖
    print("\n选择安装方式:")
    print("1. 自动安装依赖包")
    print("2. 仅测试当前环境")
    print("3. 退出")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == "1":
        # 安装conda包
        conda_success = install_with_conda()
        
        # 安装PyQt5
        pyqt_success = install_pyqt()
        
        if conda_success and pyqt_success:
            print("\n" + "=" * 60)
            print("安装完成，正在测试...")
            print("=" * 60)
            
            if test_imports():
                print("\n✓ 所有依赖包安装成功！")
                print("您现在可以运行Web应用程序:")
                print("cd webapp && python run.py")
            else:
                print("\n⚠ 部分依赖包可能存在问题")
        else:
            print("\n✗ 安装过程中出现问题")
    
    elif choice == "2":
        test_imports()
    
    elif choice == "3":
        print("退出安装程序")
    
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
