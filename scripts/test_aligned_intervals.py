#!/usr/bin/env python3
"""
测试对齐格式的位置间隔显示
"""
import sys
import os
import pandas as pd

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from analysis.p5_analysis import format_position_intervals, analyze_p5_data
from config.lottery_config import LotteryType


def test_aligned_format():
    """测试对齐格式"""
    print("=== 测试对齐格式的间隔显示 ===\n")
    
    # 测试不同的间隔数据
    test_cases = [
        [1, 12, 4, 3, 8],      # 您提供的示例
        [9, 2, 22, 2, 10],     # 混合单双位数
        [1, 1, 37, 16, 2],     # 包含大数字
        [19, 1, 18, 4, 0],     # 包含0
        [17, 9, 3, 3, 1],      # 多个单位数
        [16, 4, 6, 11, 1],     # 中等数字
        [16, 3, 15, 14, 10],   # 都是双位数
        [2, 6, 4, 1, 0],       # 小数字
        [5, 2, 4, 3, 7],       # 全单位数
        [2, 16, 1, 7, 4],      # 混合格式
    ]
    
    print("原始格式 → 对齐格式")
    print("-" * 30)
    
    for intervals in test_cases:
        # 原始格式（用于对比）
        original = "-".join(str(i) for i in intervals)
        # 对齐格式
        aligned = format_position_intervals(intervals)
        print(f"{original:<15} → {aligned}")
    
    print(f"\n对齐效果展示:")
    print("=" * 40)
    for intervals in test_cases:
        aligned = format_position_intervals(intervals)
        print(f"  {aligned}")


def test_real_data_aligned():
    """测试真实数据的对齐格式"""
    print("\n=== 真实数据对齐格式测试 ===\n")
    
    # 读取最新的15期数据
    df = pd.read_csv('data/pl5_3000.csv')
    test_data = df.tail(15).copy()
    
    # 分析数据
    analyzed_data = analyze_p5_data(test_data, LotteryType.P5)
    
    print("最新15期间隔对齐显示:")
    print("=" * 60)
    print(f"{'期号':<8} {'日期':<12} {'号码':<12} {'间隔(对齐格式)':<15}")
    print("-" * 60)
    
    for result in analyzed_data:
        numbers = f"{result['号码1']}{result['号码2']}{result['号码3']}{result['号码4']}{result['号码5']}"
        print(f"{result['期号']:<8} {result['日期']:<12} {numbers:<12} {result['间隔']:<15}")


def demonstrate_alignment_benefits():
    """演示对齐格式的优势"""
    print("\n=== 对齐格式优势演示 ===\n")
    
    # 模拟一组数据
    sample_data = [
        ("25193", "2 6 5 4 9", [3, 15, 2, 8, 1]),
        ("25194", "2 7 0 3 7", [1, 2, 22, 16, 2]),
        ("25195", "2 1 0 9 0", [1, 37, 1, 4, 18]),
        ("25196", "9 4 1 4 0", [17, 9, 3, 3, 1]),
        ("25197", "8 6 9 2 0", [16, 4, 6, 11, 1]),
        ("25198", "7 1 7 8 3", [16, 3, 15, 14, 10]),
        ("25199", "4 8 5 9 3", [2, 6, 4, 1, 7]),
        ("25200", "2 1 1 2 9", [5, 2, 4, 3, 7]),
        ("25201", "4 0 1 3 0", [2, 16, 1, 7, 4]),
        ("25202", "2 5 3 7 3", [2, 13, 10, 12, 3]),
    ]
    
    print("未对齐格式:")
    print("-" * 50)
    for period, numbers, intervals in sample_data:
        unaligned = "-".join(str(i) for i in intervals)
        print(f"{period} {numbers:<12} {unaligned}")
    
    print("\n对齐格式:")
    print("-" * 50)
    for period, numbers, intervals in sample_data:
        aligned = format_position_intervals(intervals)
        print(f"{period} {numbers:<12} {aligned}")
    
    print("\n对齐格式优势:")
    print("✅ 每列数字垂直对齐，便于比较")
    print("✅ 视觉效果更整齐，易于阅读")
    print("✅ 便于快速识别数字大小")
    print("✅ 适合表格和报表显示")


def main():
    """主函数"""
    try:
        test_aligned_format()
        test_real_data_aligned()
        demonstrate_alignment_benefits()
        
        print("\n" + "="*60)
        print("✅ 对齐格式测试完成！")
        print("间隔显示已优化为对齐格式：XX-XX-XX-XX-XX")
        print("="*60)
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
