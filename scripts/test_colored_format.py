#!/usr/bin/env python3
"""
测试彩色号码格式功能
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from analysis.p5_analysis import (
    calculate_number_format_with_color, analyze_p5_single_record,
    parse_p5_numbers
)
from view.colored_treeview import NumberFormatDisplay
from config.lottery_config import LotteryType


def test_colored_format_calculation():
    """测试彩色格式计算"""
    print("=== 测试彩色号码格式计算 ===\n")
    
    # 测试用例
    test_cases = [
        ([2, 5, 3, 7, 3], "25373", "ABCDA"),  # 第1、5位相同
        ([1, 1, 2, 3, 3], "11233", "AABCC"),  # 第1、2位相同，第4、5位相同
        ([4, 4, 4, 5, 6], "44456", "AAABC"),  # 第1、2、3位相同
        ([1, 2, 3, 4, 5], "12345", "ABCDE"),  # 无重复
        ([7, 7, 7, 7, 7], "77777", "AAAAA"),  # 全部相同
        ([9, 1, 9, 1, 9], "91919", "ABABA"),  # 交替重复
        ([6, 8, 3, 5, 7], "68357", "ABCDE"),  # 无重复
    ]
    
    print(f"{'号码':<8} {'格式':<8} {'颜色信息':<25} {'普通显示':<15} {'控制台显示'}")
    print("-" * 80)
    
    for numbers, number_str, expected in test_cases:
        # 计算格式和颜色信息
        format_info = calculate_number_format_with_color(numbers)
        
        # 生成不同显示格式
        display_manager = NumberFormatDisplay()
        plain_display = display_manager.get_display_text(
            format_info['format'], format_info['colors'], 'plain'
        )
        console_display = display_manager.get_display_text(
            format_info['format'], format_info['colors'], 'console'
        )
        
        print(f"{number_str:<8} {format_info['format']:<8} {str(format_info['colors']):<25} "
              f"{plain_display:<15} {console_display}")


def test_single_record_analysis():
    """测试单条记录分析"""
    print("\n=== 测试单条记录分析（包含颜色信息） ===\n")
    
    # 测试最新几期数据
    test_data = [
        "2 5 3 7 3",  # 25202期
        "4 0 1 3 0",  # 25201期
        "2 1 1 2 9",  # 25200期
        "4 8 5 9 3",  # 25199期
        "7 1 7 8 3",  # 25198期
    ]
    
    for numbers_str in test_data:
        numbers = parse_p5_numbers(numbers_str)
        result = analyze_p5_single_record(numbers, LotteryType.P5)
        
        print(f"号码: {numbers_str}")
        print(f"  格式: {result['号码格式']}")
        print(f"  颜色: {result['格式颜色']}")
        print(f"  彩色: {result['彩色格式']}")
        
        # 生成不同显示格式
        display_manager = NumberFormatDisplay()
        plain_display = display_manager.get_display_text(
            result['号码格式'], result['格式颜色'], 'plain'
        )
        html_display = display_manager.get_display_text(
            result['号码格式'], result['格式颜色'], 'html'
        )
        
        print(f"  普通显示: {plain_display}")
        print(f"  HTML显示: {html_display}")
        print()


def test_display_formats():
    """测试不同显示格式"""
    print("=== 测试不同显示格式 ===\n")
    
    # 示例数据
    format_chars = "AABCC"
    color_info = ['repeat', 'repeat', 'normal', 'repeat', 'repeat']
    
    display_manager = NumberFormatDisplay()
    
    print("格式字符串: AABCC")
    print("颜色信息: ['repeat', 'repeat', 'normal', 'repeat', 'repeat']")
    print()
    
    # 测试各种显示格式
    plain = display_manager.get_display_text(format_chars, color_info, 'plain')
    console = display_manager.get_display_text(format_chars, color_info, 'console')
    html = display_manager.get_display_text(format_chars, color_info, 'html')
    
    print(f"普通文本显示: {plain}")
    print(f"控制台显示: {console}")
    print(f"HTML显示: {html}")
    
    # 显示图例
    legend = display_manager.create_legend()
    print(f"\n{legend}")


def demonstrate_color_benefits():
    """演示彩色显示的优势"""
    print("\n=== 彩色显示优势演示 ===\n")
    
    # 对比数据
    examples = [
        ([1, 1, 2, 3, 3], "重复模式：AA_BB"),
        ([4, 4, 4, 5, 6], "三连重复：AAA__"),
        ([7, 8, 7, 9, 7], "间隔重复：A_A_A"),
        ([2, 2, 2, 2, 2], "全部重复：AAAAA"),
        ([1, 2, 3, 4, 5], "无重复：ABCDE"),
    ]
    
    print("传统显示 vs 彩色显示对比:")
    print("-" * 50)
    
    for numbers, description in examples:
        format_info = calculate_number_format_with_color(numbers)
        display_manager = NumberFormatDisplay()
        
        # 传统显示
        traditional = format_info['format']
        
        # 彩色显示
        colored = display_manager.get_display_text(
            format_info['format'], format_info['colors'], 'plain'
        )
        
        numbers_str = ''.join(map(str, numbers))
        print(f"{numbers_str} {description}")
        print(f"  传统: {traditional}")
        print(f"  彩色: {colored}")
        print()
    
    print("彩色显示优势:")
    print("✅ 重复字符用[方括号]标记，一目了然")
    print("✅ 快速识别号码重复模式")
    print("✅ 便于分析号码格式规律")
    print("✅ 提升数据可读性和分析效率")


def main():
    """主函数"""
    try:
        test_colored_format_calculation()
        test_single_record_analysis()
        test_display_formats()
        demonstrate_color_benefits()
        
        print("\n" + "="*60)
        print("✅ 彩色号码格式功能测试完成！")
        print("重复字符将用淡蓝色显示，便于识别号码模式")
        print("="*60)
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
