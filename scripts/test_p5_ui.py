#!/usr/bin/env python3
"""
测试排列五界面优化效果
"""
import sys
import os
import tkinter as tk
from tkinter import ttk

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from view.main_window import MainWindow
from controller.main_controller import MainController
from config.lottery_config import LotteryType, LotteryConfig


def test_ui_performance():
    """测试界面性能"""
    print("=== 测试排列五界面优化效果 ===\n")
    
    try:
        # 创建控制器
        controller = MainController()
        
        # 预加载排列五数据
        print("预加载排列五数据...")
        success = controller.load_data('data/pl5_3000.csv', LotteryType.P5)
        if not success:
            print("❌ 数据加载失败")
            return
        
        # 分析数据
        print("分析数据...")
        analyzed_data = controller.analyze_data()
        if analyzed_data is None:
            print("❌ 数据分析失败")
            return
        
        print(f"✓ 成功分析 {len(analyzed_data)} 条记录")
        
        # 创建界面
        print("创建界面...")
        app = MainWindow(controller)
        
        # 设置为排列五类型
        app.lottery_type_var.set(LotteryConfig.get_name(LotteryType.P5))
        app.on_lottery_type_changed()
        
        print("✓ 界面创建完成")
        print("\n界面优化特性:")
        print("- 限制显示最新1000条记录，避免界面卡顿")
        print("- 动态调整表格列标题适应不同彩票类型")
        print("- 排列五数据格式优化显示")
        print("- 异步数据加载和分析")
        
        print(f"\n当前显示数据:")
        print(f"- 彩票类型: {LotteryConfig.get_name(LotteryType.P5)}")
        print(f"- 总记录数: {len(analyzed_data)}")
        print(f"- 显示记录数: {min(1000, len(analyzed_data))}")
        
        # 启动界面
        print("\n启动界面...")
        app.run()
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def create_simple_test_window():
    """创建简单的测试窗口"""
    root = tk.Tk()
    root.title("排列五界面测试")
    root.geometry("800x600")
    
    # 创建说明标签
    info_label = ttk.Label(root, text="排列五界面优化测试\n\n优化内容:\n1. 限制显示记录数量(最多1000条)\n2. 动态表格列标题\n3. 排列五数据格式适配\n4. 异步数据处理", 
                          justify=tk.LEFT, font=("Arial", 12))
    info_label.pack(pady=20)
    
    # 创建测试按钮
    def start_full_test():
        root.destroy()
        test_ui_performance()
    
    test_button = ttk.Button(root, text="启动完整界面测试", command=start_full_test)
    test_button.pack(pady=10)
    
    # 创建退出按钮
    exit_button = ttk.Button(root, text="退出", command=root.destroy)
    exit_button.pack(pady=5)
    
    root.mainloop()


def main():
    """主函数"""
    print("排列五界面优化测试")
    print("="*40)
    
    choice = input("选择测试方式:\n1. 简单测试窗口\n2. 完整界面测试\n请输入选择 (1/2): ").strip()
    
    if choice == "1":
        create_simple_test_window()
    elif choice == "2":
        test_ui_performance()
    else:
        print("无效选择，启动简单测试窗口...")
        create_simple_test_window()


if __name__ == "__main__":
    main()
