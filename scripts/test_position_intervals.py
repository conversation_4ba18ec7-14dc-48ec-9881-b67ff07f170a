#!/usr/bin/env python3
"""
测试排列五位置间隔功能
"""
import sys
import os
import pandas as pd

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from analysis.p5_analysis import (
    calculate_position_intervals, format_position_intervals, 
    parse_p5_numbers, analyze_p5_data
)
from config.lottery_config import LotteryType


def test_position_intervals_calculation():
    """测试位置间隔计算功能"""
    print("=== 测试位置间隔计算功能 ===\n")
    
    # 创建测试数据
    test_data = [
        [1, 2, 3, 4, 5],  # 第1期
        [6, 7, 8, 9, 0],  # 第2期
        [1, 8, 3, 0, 5],  # 第3期：位置1的1间隔2期，位置3的3间隔2期，位置5的5间隔2期
        [2, 2, 4, 4, 6],  # 第4期
        [1, 3, 3, 5, 7],  # 第5期：位置1的1间隔2期，位置3的3间隔2期
    ]
    
    print("测试数据:")
    for i, numbers in enumerate(test_data):
        print(f"第{i+1}期: {numbers}")
    
    print("\n间隔计算结果:")
    for i, numbers in enumerate(test_data):
        intervals = calculate_position_intervals(i, numbers, test_data)
        interval_str = format_position_intervals(intervals)
        print(f"第{i+1}期 {numbers} → 间隔: {interval_str}")
        
        # 详细解释
        if i > 0:
            print("  详细分析:")
            for pos in range(5):
                current_num = numbers[pos]
                interval = intervals[pos]
                if interval == 0:
                    print(f"    位置{pos+1}的{current_num}: 首次出现")
                else:
                    # 查找上次出现的位置
                    for j in range(i-1, -1, -1):
                        if test_data[j][pos] == current_num:
                            print(f"    位置{pos+1}的{current_num}: 上次在第{j+1}期出现，间隔{i-j}期")
                            break
        print()


def test_real_data_intervals():
    """测试真实数据的间隔计算"""
    print("=== 测试真实数据间隔计算 ===\n")
    
    # 读取最新的几期数据进行测试
    df = pd.read_csv('data/pl5_3000.csv')
    
    # 取最后10期数据进行测试
    test_data = df.tail(10).copy()
    
    print("使用最新10期数据测试间隔计算:")
    print("="*80)
    print(f"{'期号':<8} {'日期':<12} {'号码':<15} {'间隔':<20}")
    print("-"*80)
    
    # 分析这10期数据
    analyzed_data = analyze_p5_data(test_data, LotteryType.P5)
    
    for result in analyzed_data:
        print(f"{result['期号']:<8} {result['日期']:<12} "
              f"{result['号码1']}{result['号码2']}{result['号码3']}{result['号码4']}{result['号码5']:<10} "
              f"{result['间隔']:<20}")


def test_specific_case():
    """测试特定案例"""
    print("\n=== 测试特定案例 ===\n")
    
    # 测试最新一期 25202: 2 5 3 7 3
    print("测试最新一期 25202: 2 5 3 7 3")
    
    # 读取数据
    df = pd.read_csv('data/pl5_3000.csv')
    
    # 找到25202期的索引
    target_period = '25202'
    target_index = None
    for idx, row in df.iterrows():
        if str(row['period']) == target_period:
            target_index = idx
            break
    
    if target_index is not None:
        # 获取所有历史号码
        all_numbers = []
        for idx, row in df.iterrows():
            if idx <= target_index:
                numbers = parse_p5_numbers(row['numbers'])
                all_numbers.append(numbers)
        
        # 计算25202期的间隔
        target_numbers = parse_p5_numbers(df.iloc[target_index]['numbers'])
        intervals = calculate_position_intervals(target_index, target_numbers, all_numbers)
        interval_str = format_position_intervals(intervals)
        
        print(f"25202期号码: {target_numbers}")
        print(f"位置间隔: {interval_str}")
        
        # 详细分析每个位置
        print("\n详细分析:")
        for pos in range(5):
            current_num = target_numbers[pos]
            interval = intervals[pos]
            print(f"位置{pos+1}的数字{current_num}:")
            
            if interval == 0:
                print(f"  首次在位置{pos+1}出现")
            else:
                # 查找上次出现的期号
                found_period = None
                for i in range(target_index - 1, -1, -1):
                    if (i < len(all_numbers) and 
                        len(all_numbers[i]) > pos and 
                        all_numbers[i][pos] == current_num):
                        found_period = df.iloc[i]['period']
                        break
                
                if found_period:
                    print(f"  上次在位置{pos+1}出现: 第{found_period}期，间隔{interval}期")
                else:
                    print(f"  间隔{interval}期（未找到具体期号）")
    else:
        print("未找到25202期数据")


def main():
    """主函数"""
    try:
        test_position_intervals_calculation()
        test_real_data_intervals()
        test_specific_case()
        
        print("\n" + "="*60)
        print("✅ 位置间隔功能测试完成！")
        print("间隔功能已成功添加到排列五分析中")
        print("="*60)
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
