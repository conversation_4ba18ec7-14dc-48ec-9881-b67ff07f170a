#!/usr/bin/env python3
"""
更新排列五数据库以支持彩色号码格式
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from controller.main_controller import MainController
from config.lottery_config import LotteryType, LotteryConfig
from model.db_manager import SQLiteManager
from view.colored_treeview import NumberFormatDisplay


def update_colored_format():
    """更新彩色格式功能"""
    print("=== 更新排列五彩色号码格式 ===\n")
    
    # 创建控制器
    controller = MainController()
    
    # 切换到排列五类型
    controller.set_lottery_type(LotteryType.P5)
    
    # 清空现有的排列五数据库记录
    print("清空现有排列五数据库记录...")
    db_manager = SQLiteManager()
    
    # 删除现有的排列五记录
    conn = db_manager.get_connection()
    if conn:
        try:
            cursor = conn.execute("DELETE FROM analysis_results WHERE lottery_type = 'p5'")
            deleted_count = cursor.rowcount
            conn.commit()
            print(f"已删除 {deleted_count} 条旧记录")
        except Exception as e:
            print(f"删除旧记录时出错: {e}")
        finally:
            conn.close()
    
    # 重新加载和分析数据
    print("\n重新加载排列五数据...")
    csv_file = LotteryConfig.get_csv_file(LotteryType.P5)
    success = controller.load_data(csv_file, LotteryType.P5)
    
    if not success:
        print("❌ 数据加载失败")
        return False
    
    print("重新分析数据（使用新的彩色格式功能）...")
    analyzed_data = controller.analyze_data()
    
    if analyzed_data is None:
        print("❌ 数据分析失败")
        return False
    
    print(f"✅ 成功重新分析 {len(analyzed_data)} 条记录")
    
    return True


def demonstrate_colored_format():
    """演示彩色格式效果"""
    print("\n=== 彩色格式效果演示 ===\n")
    
    # 从数据库加载最新数据
    db_manager = SQLiteManager()
    data = db_manager.load_results(LotteryType.P5)
    
    if data is None or len(data) == 0:
        print("❌ 无法从数据库加载数据")
        return
    
    # 显示最新10期的彩色格式效果
    latest_data = data.tail(10)
    display_manager = NumberFormatDisplay()
    
    print("最新10期彩色号码格式显示:")
    print("=" * 80)
    print(f"{'期号':<8} {'号码':<8} {'传统格式':<8} {'彩色格式':<20} {'HTML格式'}")
    print("-" * 80)
    
    for _, row in latest_data.iterrows():
        numbers = f"{row['号码1']}{row['号码2']}{row['号码3']}{row['号码4']}{row['号码5']}"
        format_str = row['号码格式']
        
        # 获取颜色信息
        if '格式颜色' in row and isinstance(row['格式颜色'], list):
            color_info = row['格式颜色']
        else:
            color_info = ['normal'] * 5
        
        # 生成不同显示格式
        plain_display = display_manager.get_display_text(format_str, color_info, 'plain')
        html_display = display_manager.get_display_text(format_str, color_info, 'html')
        
        # 截断HTML显示以适应表格
        html_short = html_display[:30] + "..." if len(html_display) > 30 else html_display
        
        print(f"{row['期号']:<8} {numbers:<8} {format_str:<8} {plain_display:<20} {html_short}")


def show_color_examples():
    """显示颜色示例"""
    print("\n=== 彩色格式示例说明 ===\n")
    
    examples = [
        ("25373", "ABCDC", "AB[C]D[C]", "第3、5位相同(数字3)"),
        ("40130", "ABCDB", "A[B]CD[B]", "第2、5位相同(数字0)"),
        ("21129", "ABBAC", "[A][B][B][A]C", "第1、4位相同(数字2)，第2、3位相同(数字1)"),
        ("48593", "ABCDE", "ABCDE", "无重复号码"),
        ("71783", "ABACD", "[A]B[A]CD", "第1、3位相同(数字7)"),
    ]
    
    print("彩色格式说明:")
    print("- 普通字符：单独出现的号码")
    print("- [蓝色字符]：重复出现的号码（用方括号标记）")
    print()
    
    print(f"{'号码':<8} {'格式':<8} {'彩色显示':<15} {'说明'}")
    print("-" * 60)
    
    for number, format_str, colored, description in examples:
        print(f"{number:<8} {format_str:<8} {colored:<15} {description}")
    
    print("\n优势:")
    print("✅ 重复号码一目了然")
    print("✅ 快速识别号码模式")
    print("✅ 便于分析重复规律")
    print("✅ 提升分析效率")


def main():
    """主函数"""
    try:
        # 更新彩色格式功能
        success = update_colored_format()
        
        if success:
            # 演示彩色格式效果
            demonstrate_colored_format()
            show_color_examples()
            
            print("\n" + "="*60)
            print("✅ 排列五彩色号码格式功能更新完成！")
            print("重复号码将用淡蓝色显示，便于识别模式")
            print("="*60)
        else:
            print("\n❌ 彩色格式功能更新失败")
        
    except Exception as e:
        print(f"❌ 更新过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
