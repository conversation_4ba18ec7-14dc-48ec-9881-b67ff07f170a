#!/usr/bin/env python3
"""
更新排列五数据库中的间隔格式为对齐格式
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from controller.main_controller import MainController
from config.lottery_config import LotteryType, LotteryConfig
from model.db_manager import SQLiteManager


def update_p5_intervals():
    """更新排列五间隔格式"""
    print("=== 更新排列五间隔格式 ===\n")
    
    # 创建控制器
    controller = MainController()
    
    # 切换到排列五类型
    controller.set_lottery_type(LotteryType.P5)
    
    # 清空现有的排列五数据库记录
    print("清空现有排列五数据库记录...")
    db_manager = SQLiteManager()
    
    # 删除现有的排列五记录
    conn = db_manager.get_connection()
    if conn:
        try:
            cursor = conn.execute("DELETE FROM analysis_results WHERE lottery_type = 'p5'")
            deleted_count = cursor.rowcount
            conn.commit()
            print(f"已删除 {deleted_count} 条旧记录")
        except Exception as e:
            print(f"删除旧记录时出错: {e}")
        finally:
            conn.close()
    
    # 重新加载和分析数据
    print("\n重新加载排列五数据...")
    csv_file = LotteryConfig.get_csv_file(LotteryType.P5)
    success = controller.load_data(csv_file, LotteryType.P5)
    
    if not success:
        print("❌ 数据加载失败")
        return False
    
    print("重新分析数据（使用新的对齐间隔格式）...")
    analyzed_data = controller.analyze_data()
    
    if analyzed_data is None:
        print("❌ 数据分析失败")
        return False
    
    print(f"✅ 成功重新分析 {len(analyzed_data)} 条记录")
    
    # 显示最新几期的对齐格式
    print("\n最新10期间隔对齐格式:")
    print("=" * 70)
    print(f"{'期号':<8} {'日期':<12} {'号码':<12} {'间隔(对齐格式)':<20}")
    print("-" * 70)
    
    latest_data = analyzed_data.tail(10)
    for _, row in latest_data.iterrows():
        numbers = f"{row['号码1']}{row['号码2']}{row['号码3']}{row['号码4']}{row['号码5']}"
        print(f"{row['期号']:<8} {row['日期']:<12} {numbers:<12} {row['间隔']:<20}")
    
    return True


def verify_alignment():
    """验证对齐效果"""
    print("\n=== 验证对齐效果 ===\n")
    
    # 从数据库加载最新数据
    db_manager = SQLiteManager()
    data = db_manager.load_results(LotteryType.P5)
    
    if data is None or len(data) == 0:
        print("❌ 无法从数据库加载数据")
        return
    
    # 显示最新15期的间隔对齐效果
    latest_data = data.tail(15)
    
    print("最新15期间隔对齐显示效果:")
    print("=" * 60)
    
    for _, row in latest_data.iterrows():
        numbers = f"{row['号码1']}{row['号码2']}{row['号码3']}{row['号码4']}{row['号码5']}"
        print(f"{row['期号']} {row['日期']} {numbers} {row['间隔']}")
    
    print("\n对齐格式特点:")
    print("✅ 每个位置固定2位数字")
    print("✅ 不足2位的前面补0")
    print("✅ 使用'-'分隔各位置")
    print("✅ 格式统一：XX-XX-XX-XX-XX")


def main():
    """主函数"""
    try:
        # 更新间隔格式
        success = update_p5_intervals()
        
        if success:
            # 验证对齐效果
            verify_alignment()
            
            print("\n" + "="*60)
            print("✅ 排列五间隔格式更新完成！")
            print("所有间隔数据已更新为对齐格式：XX-XX-XX-XX-XX")
            print("="*60)
        else:
            print("\n❌ 间隔格式更新失败")
        
    except Exception as e:
        print(f"❌ 更新过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
