#!/usr/bin/env python3
"""
验证连号计算逻辑
专门验证68357这个号码的连号计算过程
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from analysis.p5_analysis import calculate_consecutive_count, parse_p5_numbers, analyze_p5_single_record
from config.lottery_config import LotteryType


def detailed_consecutive_analysis(numbers):
    """详细分析连号计算过程"""
    print(f"原始号码: {numbers}")
    
    # 排序号码
    sorted_nums = sorted(numbers)
    print(f"排序后号码: {sorted_nums}")
    
    # 逐步分析连号过程
    consecutive_count = 0
    current_consecutive = 1
    consecutive_groups = []
    current_group = [sorted_nums[0]]
    
    print("\n连号分析过程:")
    for i in range(1, len(sorted_nums)):
        print(f"  比较 {sorted_nums[i-1]} 和 {sorted_nums[i]}")
        
        if sorted_nums[i] == sorted_nums[i-1] + 1:
            current_consecutive += 1
            current_group.append(sorted_nums[i])
            print(f"    连续! 当前连号长度: {current_consecutive}")
        else:
            if current_consecutive >= 2:
                consecutive_groups.append(current_group.copy())
                consecutive_count = max(consecutive_count, current_consecutive)
                print(f"    发现连号组: {current_group}, 长度: {current_consecutive}")
            
            current_consecutive = 1
            current_group = [sorted_nums[i]]
            print(f"    不连续，重新开始计数")
    
    # 检查最后一组
    if current_consecutive >= 2:
        consecutive_groups.append(current_group.copy())
        consecutive_count = max(consecutive_count, current_consecutive)
        print(f"    最后一组连号: {current_group}, 长度: {current_consecutive}")
    
    print(f"\n连号组: {consecutive_groups}")
    print(f"最大连号长度: {consecutive_count}")
    
    return consecutive_count


def verify_specific_numbers():
    """验证特定号码的连号计算"""
    test_cases = [
        "6 8 3 5 7",  # 68357
        "1 2 3 4 5",  # 12345 (全连)
        "1 3 5 7 9",  # 13579 (无连号)
        "1 2 4 5 6",  # 12456 (有两组连号)
        "5 1 4 2 3",  # 51423 (重新排列后是12345)
    ]
    
    for numbers_str in test_cases:
        print("="*60)
        print(f"测试号码: {numbers_str}")
        print("="*60)
        
        numbers = parse_p5_numbers(numbers_str)
        
        # 详细分析
        calculated_consecutive = detailed_consecutive_analysis(numbers)
        
        # 使用系统函数验证
        system_consecutive = calculate_consecutive_count(numbers)
        
        print(f"\n系统计算结果: {system_consecutive}")
        print(f"详细分析结果: {calculated_consecutive}")
        
        if calculated_consecutive == system_consecutive:
            print("✅ 计算结果一致")
        else:
            print("❌ 计算结果不一致")
        
        # 完整分析
        full_analysis = analyze_p5_single_record(numbers, LotteryType.P5)
        print(f"\n完整分析结果:")
        print(f"  奇偶比: {full_analysis['奇偶比']}")
        print(f"  奇偶排布: {full_analysis['奇偶排布']}")
        print(f"  大小比: {full_analysis['大小比']}")
        print(f"  大小排布: {full_analysis['大小排布']}")
        print(f"  连号: {full_analysis['连号']}")
        print(f"  重号: {full_analysis['重号']}")
        print(f"  号码格式: {full_analysis['号码格式']}")
        print()


def analyze_68357_specifically():
    """专门分析68357"""
    print("="*60)
    print("专门分析 68357 的连号计算")
    print("="*60)
    
    numbers = [6, 8, 3, 5, 7]
    print(f"原始号码: {numbers}")
    
    # 手动分析过程
    print("\n手动分析过程:")
    print("1. 排序号码: [3, 5, 6, 7, 8]")
    print("2. 检查连续性:")
    print("   - 3 和 5: 不连续 (差值=2)")
    print("   - 5 和 6: 连续 (差值=1) ✓")
    print("   - 6 和 7: 连续 (差值=1) ✓")
    print("   - 7 和 8: 连续 (差值=1) ✓")
    print("3. 连号组: [5, 6, 7, 8]")
    print("4. 连号长度: 4")
    
    # 系统计算
    system_result = calculate_consecutive_count(numbers)
    print(f"\n系统计算结果: {system_result}")
    
    # 详细分析
    detailed_result = detailed_consecutive_analysis(numbers)
    print(f"详细分析结果: {detailed_result}")
    
    if system_result == 4:
        print("\n✅ 68357的连号计算正确: 4连号 (5-6-7-8)")
    else:
        print(f"\n❌ 68357的连号计算可能有问题，期望4，实际{system_result}")


def main():
    """主函数"""
    print("连号计算验证工具")
    print("="*60)
    
    # 专门分析68357
    analyze_68357_specifically()
    
    print("\n" + "="*60)
    print("其他测试用例验证")
    print("="*60)
    
    # 验证其他号码
    verify_specific_numbers()


if __name__ == "__main__":
    main()
