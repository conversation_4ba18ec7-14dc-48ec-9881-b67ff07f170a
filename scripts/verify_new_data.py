#!/usr/bin/env python3
"""
验证新添加的排列五数据
"""
import sys
import os
import pandas as pd

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from analysis.p5_analysis import parse_p5_numbers, analyze_p5_single_record
from config.lottery_config import LotteryType


def verify_new_data():
    """验证新添加的数据"""
    print("=== 验证新添加的排列五数据 ===\n")
    
    # 读取数据文件
    df = pd.read_csv('data/pl5_3000.csv')
    print(f"数据文件总行数: {len(df)} (包含标题行)")
    print(f"数据期间: {df['date'].min()} 至 {df['date'].max()}")
    
    # 获取最新的11条记录（新添加的数据）
    latest_data = df.tail(11)
    
    print(f"\n最新添加的11期数据:")
    print("="*80)
    print(f"{'期号':<8} {'日期':<12} {'号码':<12} {'和值':<4} {'奇偶比':<8} {'大小比':<8} {'连号':<4} {'重号':<4}")
    print("-"*80)
    
    for _, row in latest_data.iterrows():
        numbers = parse_p5_numbers(row['numbers'])
        analysis = analyze_p5_single_record(numbers, LotteryType.P5)
        
        print(f"{row['period']:<8} {row['date']:<12} {row['numbers']:<12} {row['sum_value']:<4} "
              f"{analysis['奇偶比']:<8} {analysis['大小比']:<8} {analysis['连号']:<4} {analysis['重号']:<4}")
    
    print("\n详细分析最新一期 (25202):")
    print("="*50)
    latest_row = latest_data.iloc[-1]
    numbers = parse_p5_numbers(latest_row['numbers'])
    analysis = analyze_p5_single_record(numbers, LotteryType.P5)
    
    print(f"期号: {latest_row['period']}")
    print(f"日期: {latest_row['date']}")
    print(f"号码: {latest_row['numbers']} → {numbers}")
    print(f"和值: {latest_row['sum_value']}")
    print(f"奇偶比: {analysis['奇偶比']}")
    print(f"奇偶排布: {analysis['奇偶排布']}")
    print(f"奇偶码: {analysis['奇偶码']}")
    print(f"大小比: {analysis['大小比']}")
    print(f"大小排布: {analysis['大小排布']}")
    print(f"大小码: {analysis['大小码']}")
    print(f"连号: {analysis['连号']}")
    print(f"重号: {analysis['重号']}")
    print(f"号码格式: {analysis['号码格式']}")


def verify_data_integrity():
    """验证数据完整性"""
    print("\n=== 数据完整性验证 ===\n")
    
    df = pd.read_csv('data/pl5_3000.csv')
    
    # 检查期号连续性
    periods = df['period'].tolist()
    missing_periods = []
    
    for i in range(1, len(periods)):
        if periods[i] != periods[i-1] + 1:
            missing_periods.append(f"期号跳跃: {periods[i-1]} → {periods[i]}")
    
    if missing_periods:
        print("发现期号不连续:")
        for missing in missing_periods[-5:]:  # 只显示最后5个
            print(f"  {missing}")
    else:
        print("✅ 期号连续性检查通过")
    
    # 检查日期格式
    try:
        df['date'] = pd.to_datetime(df['date'])
        print("✅ 日期格式检查通过")
    except Exception as e:
        print(f"❌ 日期格式错误: {e}")
    
    # 检查号码格式
    invalid_numbers = []
    for idx, row in df.iterrows():
        try:
            numbers = parse_p5_numbers(row['numbers'])
            if len(numbers) != 5:
                invalid_numbers.append(f"第{idx+1}行: {row['numbers']}")
            elif any(n < 0 or n > 9 for n in numbers):
                invalid_numbers.append(f"第{idx+1}行: {row['numbers']} (号码超出0-9范围)")
        except Exception as e:
            invalid_numbers.append(f"第{idx+1}行: {row['numbers']} (解析错误)")
    
    if invalid_numbers:
        print("发现无效号码:")
        for invalid in invalid_numbers[-5:]:  # 只显示最后5个
            print(f"  {invalid}")
    else:
        print("✅ 号码格式检查通过")
    
    print(f"\n数据统计:")
    print(f"  总记录数: {len(df)}")
    print(f"  期号范围: {df['period'].min()} - {df['period'].max()}")
    print(f"  日期范围: {df['date'].min()} - {df['date'].max()}")


def main():
    """主函数"""
    try:
        verify_new_data()
        verify_data_integrity()
        
        print("\n" + "="*50)
        print("✅ 新数据验证完成！")
        print("数据已成功添加到 pl5_3000.csv 文件中")
        print("="*50)
        
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
