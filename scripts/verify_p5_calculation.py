#!/usr/bin/env python3
"""
验证排列五计算结果的准确性
"""
import sys
import os
import pandas as pd

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from analysis.p5_analysis import parse_p5_numbers, analyze_p5_single_record
from config.lottery_config import LotteryType


def verify_sample_calculations():
    """验证样本计算的准确性"""
    print("=== 验证排列五计算准确性 ===\n")
    
    # 读取原始数据
    df = pd.read_csv('data/pl5_3000.csv')
    print(f"数据文件总行数: {len(df)} (包含标题行)")
    print(f"实际数据行数: {len(df)} 行")
    print(f"数据期间: {df['date'].min()} 至 {df['date'].max()}\n")
    
    # 验证几个具体的计算示例
    test_cases = [
        ("2008-07-23", "0 6 8 6 5"),  # 第一条数据
        ("2008-07-24", "4 8 1 2 6"),  # 第二条数据
        ("2025-07-21", "6 8 3 5 7"),  # 最后一条数据
    ]
    
    for date, numbers_str in test_cases:
        print(f"验证 {date}: {numbers_str}")
        numbers = parse_p5_numbers(numbers_str)
        result = analyze_p5_single_record(numbers, LotteryType.P5)
        
        print(f"  号码: {numbers}")
        print(f"  奇偶比: {result['奇偶比']}")
        print(f"  奇偶排布: {result['奇偶排布']}")
        print(f"  奇偶码: {result['奇偶码']}")
        print(f"  大小比: {result['大小比']}")
        print(f"  大小排布: {result['大小排布']}")
        print(f"  大小码: {result['大小码']}")
        print(f"  连号: {result['连号']}")
        print(f"  重号: {result['重号']}")
        print(f"  号码格式: {result['号码格式']}")
        print()


def verify_statistics():
    """验证统计结果"""
    print("=== 验证统计结果 ===\n")
    
    # 读取数据并进行分析
    df = pd.read_csv('data/pl5_3000.csv')
    
    # 手动统计奇偶比
    odd_even_counts = {}
    big_small_counts = {}
    consecutive_counts = {}
    repeat_counts = {}
    
    for _, row in df.iterrows():
        numbers = parse_p5_numbers(row['numbers'])
        result = analyze_p5_single_record(numbers, LotteryType.P5)
        
        # 统计奇偶比
        odd_even_ratio = result['奇偶比']
        odd_even_counts[odd_even_ratio] = odd_even_counts.get(odd_even_ratio, 0) + 1
        
        # 统计大小比
        big_small_ratio = result['大小比']
        big_small_counts[big_small_ratio] = big_small_counts.get(big_small_ratio, 0) + 1
        
        # 统计连号
        consecutive = result['连号']
        consecutive_counts[consecutive] = consecutive_counts.get(consecutive, 0) + 1
        
        # 统计重号
        repeat = result['重号']
        repeat_counts[repeat] = repeat_counts.get(repeat, 0) + 1
    
    total = len(df)
    
    print("奇偶比分布验证:")
    for ratio in sorted(odd_even_counts.keys()):
        count = odd_even_counts[ratio]
        percentage = count / total * 100
        print(f"  {ratio}: {count}期 ({percentage:.1f}%)")
    
    print("\n大小比分布验证:")
    for ratio in sorted(big_small_counts.keys()):
        count = big_small_counts[ratio]
        percentage = count / total * 100
        print(f"  {ratio}: {count}期 ({percentage:.1f}%)")
    
    print("\n连号分布验证:")
    for consecutive in sorted(consecutive_counts.keys()):
        count = consecutive_counts[consecutive]
        percentage = count / total * 100
        print(f"  {consecutive}连号: {count}期 ({percentage:.1f}%)")
    
    print("\n重号分布验证:")
    for repeat in sorted(repeat_counts.keys()):
        count = repeat_counts[repeat]
        percentage = count / total * 100
        print(f"  {repeat}重号: {count}期 ({percentage:.1f}%)")


def main():
    """主函数"""
    try:
        verify_sample_calculations()
        verify_statistics()
        
        print("\n" + "="*50)
        print("✅ 计算结果验证完成！")
        print("所有计算都基于 pl5_3000.csv 文件的真实数据")
        print("="*50)
        
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
