#!/usr/bin/env python3
"""
排列五功能集成测试
验证排列五数据分析功能的正确性和兼容性
"""
import sys
import os
import pandas as pd
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from config.lottery_config import LotteryType, LotteryConfig
from model.data_model import LotteryDataModel
from model.db_manager import SQLiteManager
from analysis.analyzer import LotteryAnalyzer
from analysis.p5_analysis import (
    parse_p5_numbers, calculate_odd_even_ratio, calculate_odd_even_pattern,
    calculate_odd_even_code, calculate_big_small_ratio, calculate_big_small_pattern,
    calculate_big_small_code, calculate_consecutive_count, calculate_repeat_count,
    calculate_number_format, analyze_p5_single_record
)
from controller.main_controller import MainController


def test_p5_config():
    """测试排列五配置"""
    print("=== 测试排列五配置 ===")
    
    # 测试枚举
    assert LotteryType.P5.value == "p5"
    print("✓ 排列五枚举类型正确")
    
    # 测试配置获取
    config = LotteryConfig.get_config(LotteryType.P5)
    assert config['name'] == '排列五'
    assert config['number_range'] == (0, 9)
    assert config['position_count'] == 5
    print("✓ 排列五配置参数正确")
    
    # 测试专用方法
    assert LotteryConfig.get_number_range(LotteryType.P5) == (0, 9)
    assert LotteryConfig.get_position_count(LotteryType.P5) == 5
    assert LotteryConfig.get_big_small_threshold(LotteryType.P5) == 5
    assert LotteryConfig.is_p5_type(LotteryType.P5) == True
    assert LotteryConfig.is_p5_type(LotteryType.DLT) == False
    print("✓ 排列五专用配置方法正确")


def test_p5_analysis_functions():
    """测试排列五分析函数"""
    print("\n=== 测试排列五分析函数 ===")
    
    # 测试数据
    test_numbers = [1, 2, 3, 4, 5]
    
    # 测试号码解析
    numbers = parse_p5_numbers("1 2 3 4 5")
    assert numbers == [1, 2, 3, 4, 5]
    print("✓ 号码解析功能正确")
    
    # 测试奇偶分析
    odd_even_ratio = calculate_odd_even_ratio(test_numbers)
    assert odd_even_ratio == "3:2"  # 1,3,5为奇数，2,4为偶数
    
    odd_even_pattern = calculate_odd_even_pattern(test_numbers)
    assert odd_even_pattern == "奇偶奇偶奇"
    
    odd_even_code = calculate_odd_even_code(test_numbers)
    assert odd_even_code == 21  # 10101 = 21
    print("✓ 奇偶分析功能正确")
    
    # 测试大小分析
    big_small_ratio = calculate_big_small_ratio(test_numbers, 5)
    assert big_small_ratio == "1:4"  # 只有5是大数
    
    big_small_pattern = calculate_big_small_pattern(test_numbers, 5)
    assert big_small_pattern == "小小小小大"
    
    big_small_code = calculate_big_small_code(test_numbers, 5)
    assert big_small_code == 1  # 00001 = 1
    print("✓ 大小分析功能正确")
    
    # 测试连号分析
    consecutive_count = calculate_consecutive_count(test_numbers)
    assert consecutive_count == 5  # 1,2,3,4,5全连
    
    consecutive_count2 = calculate_consecutive_count([1, 3, 5, 7, 9])
    assert consecutive_count2 == 0  # 无连号
    print("✓ 连号分析功能正确")
    
    # 测试重号分析
    repeat_count = calculate_repeat_count([1, 1, 2, 3, 3])
    assert repeat_count == 4  # 1出现2次，3出现2次
    
    repeat_count2 = calculate_repeat_count(test_numbers)
    assert repeat_count2 == 0  # 无重号
    print("✓ 重号分析功能正确")
    
    # 测试号码格式
    number_format = calculate_number_format([1, 1, 2, 3, 3])
    assert len(number_format) == 5
    print("✓ 号码格式分析功能正确")


def test_p5_single_record_analysis():
    """测试单条记录分析"""
    print("\n=== 测试单条记录分析 ===")
    
    test_numbers = [1, 2, 3, 4, 5]
    result = analyze_p5_single_record(test_numbers, LotteryType.P5)
    
    expected_keys = [
        '奇偶比', '奇偶排布', '奇偶码', '大小比', '大小排布', 
        '大小码', '连号', '重号', '号码格式'
    ]
    
    for key in expected_keys:
        assert key in result
    
    assert result['奇偶比'] == "3:2"
    assert result['奇偶排布'] == "奇偶奇偶奇"
    assert result['大小比'] == "1:4"
    assert result['连号'] == 5
    assert result['重号'] == 0
    
    print("✓ 单条记录分析功能正确")


def test_p5_data_loading():
    """测试排列五数据加载"""
    print("\n=== 测试排列五数据加载 ===")
    
    # 创建数据模型
    data_model = LotteryDataModel()
    
    # 加载排列五数据
    success = data_model.load_csv_data('data/pl5_3000.csv', LotteryType.P5)
    assert success, "排列五数据加载失败"
    
    # 检查数据
    raw_data = data_model.get_raw_data()
    assert raw_data is not None
    assert len(raw_data) > 0
    
    print(f"✓ 成功加载排列五数据: {len(raw_data)} 条记录")


def test_p5_analysis_integration():
    """测试排列五分析集成"""
    print("\n=== 测试排列五分析集成 ===")
    
    # 创建分析器
    analyzer = LotteryAnalyzer()
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'date': ['2024-01-01', '2024-01-02'],
        'period': ['24001', '24002'],
        'numbers': ['1 2 3 4 5', '6 7 8 9 0']
    })
    
    # 进行分析
    result = analyzer.analyze_data(test_data, LotteryType.P5)
    
    assert result is not None
    assert len(result) == 2
    
    # 检查必要的列
    expected_columns = [
        '日期', '期号', '号码1', '号码2', '号码3', '号码4', '号码5',
        '奇偶比', '奇偶排布', '奇偶码', '大小比', '大小排布', '大小码',
        '连号', '重号', '号码格式'
    ]
    
    for col in expected_columns:
        assert col in result.columns, f"缺少列: {col}"
    
    print("✓ 排列五分析集成功能正确")


def test_p5_database_operations():
    """测试排列五数据库操作"""
    print("\n=== 测试排列五数据库操作 ===")
    
    # 创建数据库管理器
    db_manager = SQLiteManager("data/test_p5.db")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        '日期': ['2024-01-01', '2024-01-02'],
        '期号': ['24001', '24002'],
        '号码1': [1, 6],
        '号码2': [2, 7],
        '号码3': [3, 8],
        '号码4': [4, 9],
        '号码5': [5, 0],
        '奇偶比': ['3:2', '2:3'],
        '奇偶排布': ['奇偶奇偶奇', '偶奇偶奇偶'],
        '奇偶码': [21, 10],
        '大小比': ['1:4', '4:1'],
        '大小排布': ['小小小小大', '大大大大小'],
        '大小码': [1, 30],
        '连号': [5, 0],
        '重号': [0, 0],
        '号码格式': ['ABCDE', 'FGHIJ'],
        '上次奇偶排布': ['无', '无'],
        '上次大小排布': ['无', '无']
    })
    
    # 保存数据
    success = db_manager.save_results(test_data, LotteryType.P5)
    assert success, "排列五数据保存失败"
    
    # 加载数据
    loaded_data = db_manager.load_results(LotteryType.P5)
    assert loaded_data is not None
    assert len(loaded_data) == 2
    
    print("✓ 排列五数据库操作功能正确")
    
    # 清理测试数据库
    if os.path.exists("data/test_p5.db"):
        os.remove("data/test_p5.db")


def test_p5_controller_integration():
    """测试排列五控制器集成"""
    print("\n=== 测试排列五控制器集成 ===")
    
    # 创建主控制器
    controller = MainController()
    
    # 切换到排列五类型
    controller.set_lottery_type(LotteryType.P5)
    assert controller.get_lottery_type() == LotteryType.P5
    
    print("✓ 排列五控制器集成功能正确")


def run_all_tests():
    """运行所有测试"""
    print("开始排列五功能集成测试...\n")
    
    try:
        test_p5_config()
        test_p5_analysis_functions()
        test_p5_single_record_analysis()
        test_p5_data_loading()
        test_p5_analysis_integration()
        test_p5_database_operations()
        test_p5_controller_integration()
        
        print("\n" + "="*50)
        print("🎉 所有排列五功能测试通过！")
        print("排列五系统扩展成功完成")
        print("="*50)
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    run_all_tests()
