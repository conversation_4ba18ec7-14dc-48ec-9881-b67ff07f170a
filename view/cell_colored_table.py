"""
基于Canvas的真正支持单元格颜色的表格组件
"""
import tkinter as tk
from tkinter import ttk
from typing import Dict, List, Any, Tuple, Optional
import math


class CellColoredTable(tk.Frame):
    """支持单元格级别颜色的表格组件"""
    
    def __init__(self, parent, columns: List[str], **kwargs):
        super().__init__(parent, **kwargs)
        
        self.columns = columns
        self.column_count = len(columns)
        self.row_count = 0
        
        # 表格数据和颜色
        self.data = []
        self.cell_colors = {}  # {(row, col): color}
        self.cell_text_colors = {}  # {(row, col): text_color}
        
        # 显示设置
        self.cell_width = 80
        self.cell_height = 25
        self.header_height = 30
        self.font = ('Arial', 9)
        self.header_font = ('Arial', 9, 'bold')
        
        # 颜色定义
        self.colors = {
            'normal': '#ffffff',
            'repeat': '#add8e6',  # 淡蓝色
            'odd': '#f0f8ff',     # 爱丽丝蓝
            'even': '#fff0f5',    # 薰衣草红
            'big': '#ffe4e1',     # 雾玫瑰
            'small': '#e0ffe0',   # 淡绿色
            'highlight': '#ffff00', # 黄色
            'warning': '#ffa500',   # 橙色
            'error': '#ff0000',     # 红色
            'success': '#90ee90',   # 淡绿色
        }
        
        self.text_colors = {
            'normal': '#000000',
            'repeat': '#000080',
            'odd': '#0000ff',
            'even': '#800080',
            'big': '#ff0000',
            'small': '#008000',
            'highlight': '#000000',
            'warning': '#ffffff',
            'error': '#ffffff',
            'success': '#006400',
        }
        
        # 创建界面
        self.create_widgets()
        
        # 绑定事件
        self.canvas.bind('<Button-1>', self.on_click)
        self.canvas.bind('<Motion>', self.on_motion)
        self.canvas.bind('<MouseWheel>', self.on_mousewheel)
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建Canvas和滚动条
        self.canvas = tk.Canvas(self, bg='white')
        self.v_scrollbar = ttk.Scrollbar(self, orient=tk.VERTICAL, command=self.canvas.yview)
        self.h_scrollbar = ttk.Scrollbar(self, orient=tk.HORIZONTAL, command=self.canvas.xview)
        
        self.canvas.configure(yscrollcommand=self.v_scrollbar.set, 
                             xscrollcommand=self.h_scrollbar.set)
        
        # 布局
        self.canvas.grid(row=0, column=0, sticky='nsew')
        self.v_scrollbar.grid(row=0, column=1, sticky='ns')
        self.h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        self.grid_rowconfigure(0, weight=1)
        self.grid_columnconfigure(0, weight=1)
        
        # 绘制表头
        self.draw_headers()
    
    def draw_headers(self):
        """绘制表头"""
        self.canvas.delete('header')
        
        for col, column_name in enumerate(self.columns):
            x1 = col * self.cell_width
            y1 = 0
            x2 = x1 + self.cell_width
            y2 = self.header_height
            
            # 绘制表头背景
            self.canvas.create_rectangle(x1, y1, x2, y2, 
                                       fill='#e0e0e0', outline='black', tags='header')
            
            # 绘制表头文本
            self.canvas.create_text(x1 + self.cell_width//2, y1 + self.header_height//2,
                                  text=column_name, font=self.header_font, tags='header')
    
    def add_row(self, values: List[Any], row_colors: Optional[Dict[int, str]] = None):
        """添加一行数据"""
        if len(values) != self.column_count:
            raise ValueError(f"期望 {self.column_count} 列，实际收到 {len(values)} 列")
        
        self.data.append(values)
        row_index = self.row_count
        self.row_count += 1
        
        # 设置单元格颜色
        if row_colors:
            for col, color in row_colors.items():
                self.set_cell_color(row_index, col, color)
        
        # 重绘表格
        self.redraw_table()
    
    def set_cell_color(self, row: int, col: int, color: str):
        """设置单元格颜色"""
        self.cell_colors[(row, col)] = color
        if color in self.text_colors:
            self.cell_text_colors[(row, col)] = self.text_colors[color]
    
    def get_cell_color(self, row: int, col: int) -> str:
        """获取单元格颜色"""
        return self.cell_colors.get((row, col), 'normal')
    
    def get_cell_text_color(self, row: int, col: int) -> str:
        """获取单元格文本颜色"""
        return self.cell_text_colors.get((row, col), self.text_colors['normal'])
    
    def redraw_table(self):
        """重绘整个表格"""
        # 清除旧的单元格
        self.canvas.delete('cell')
        
        # 绘制数据行
        for row in range(self.row_count):
            for col in range(self.column_count):
                self.draw_cell(row, col)
        
        # 更新滚动区域
        self.update_scroll_region()
    
    def draw_cell(self, row: int, col: int):
        """绘制单个单元格"""
        x1 = col * self.cell_width
        y1 = self.header_height + row * self.cell_height
        x2 = x1 + self.cell_width
        y2 = y1 + self.cell_height
        
        # 获取颜色
        bg_color = self.colors.get(self.get_cell_color(row, col), self.colors['normal'])
        text_color = self.get_cell_text_color(row, col)
        
        # 绘制单元格背景
        self.canvas.create_rectangle(x1, y1, x2, y2, 
                                   fill=bg_color, outline='black', tags='cell')
        
        # 绘制单元格文本
        if row < len(self.data) and col < len(self.data[row]):
            text = str(self.data[row][col])
            self.canvas.create_text(x1 + self.cell_width//2, y1 + self.cell_height//2,
                                  text=text, font=self.font, fill=text_color, tags='cell')
    
    def update_scroll_region(self):
        """更新滚动区域"""
        width = self.column_count * self.cell_width
        height = self.header_height + self.row_count * self.cell_height
        self.canvas.configure(scrollregion=(0, 0, width, height))
    
    def on_click(self, event):
        """处理点击事件"""
        # 转换为Canvas坐标
        x = self.canvas.canvasx(event.x)
        y = self.canvas.canvasy(event.y)
        
        # 计算单元格位置
        if y < self.header_height:
            return  # 点击在表头
        
        col = int(x // self.cell_width)
        row = int((y - self.header_height) // self.cell_height)
        
        if 0 <= row < self.row_count and 0 <= col < self.column_count:
            self.on_cell_click(row, col)
    
    def on_cell_click(self, row: int, col: int):
        """单元格点击事件（可重写）"""
        print(f"点击了单元格 ({row}, {col}): {self.data[row][col]}")
        
        # 示例：切换颜色
        current_color = self.get_cell_color(row, col)
        if current_color == 'normal':
            self.set_cell_color(row, col, 'highlight')
        elif current_color == 'highlight':
            self.set_cell_color(row, col, 'repeat')
        else:
            self.set_cell_color(row, col, 'normal')
        
        self.draw_cell(row, col)
    
    def on_motion(self, event):
        """处理鼠标移动事件"""
        # 可以在这里添加悬停效果
        pass
    
    def on_mousewheel(self, event):
        """处理鼠标滚轮事件"""
        self.canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
    
    def apply_number_format_colors(self, row: int, format_chars: str, color_info: List[str]):
        """应用号码格式颜色"""
        # 假设号码在列1-5，格式在列6
        for i, (char, color) in enumerate(zip(format_chars, color_info)):
            if i < 5:  # 号码列
                self.set_cell_color(row, i + 1, color)
        
        # 格式列使用特殊显示
        if len(color_info) > 0:
            format_color = 'repeat' if 'repeat' in color_info else 'normal'
            self.set_cell_color(row, 6, format_color)


class P5ColoredTable(CellColoredTable):
    """排列五专用彩色表格"""
    
    def __init__(self, parent, **kwargs):
        columns = ['期号', '号码1', '号码2', '号码3', '号码4', '号码5', '号码格式', '奇偶比', '大小比']
        super().__init__(parent, columns, **kwargs)
        
        # 设置列宽
        self.cell_width = 70
    
    def add_p5_record(self, issue: str, numbers: List[int], format_chars: str, 
                      color_info: List[str], odd_even_ratio: str, big_small_ratio: str):
        """添加排列五记录"""
        values = [issue] + numbers + [format_chars, odd_even_ratio, big_small_ratio]
        
        # 计算颜色
        row_colors = {}
        
        # 号码颜色（基于奇偶）
        for i, num in enumerate(numbers):
            if i < len(color_info):
                if color_info[i] == 'repeat':
                    row_colors[i + 1] = 'repeat'
                elif num % 2 == 1:
                    row_colors[i + 1] = 'odd'
                else:
                    row_colors[i + 1] = 'even'
        
        # 格式列颜色
        if 'repeat' in color_info:
            row_colors[6] = 'repeat'
        
        self.add_row(values, row_colors)


def demo_cell_colored_table():
    """演示单元格彩色表格"""
    root = tk.Tk()
    root.title("单元格彩色表格演示")
    root.geometry("900x600")
    
    # 创建主框架
    main_frame = ttk.Frame(root, padding="10")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="排列五号码真彩色显示", 
                           font=('Arial', 14, 'bold'))
    title_label.pack(pady=10)
    
    # 创建彩色表格
    table = P5ColoredTable(main_frame)
    table.pack(fill=tk.BOTH, expand=True, pady=10)
    
    # 添加示例数据
    sample_data = [
        ('25001', [1, 1, 2, 3, 4], 'AABCD', ['repeat', 'repeat', 'normal', 'normal', 'normal'], '3:2', '2:3'),
        ('25002', [2, 4, 6, 8, 0], 'ABCDE', ['normal', 'normal', 'normal', 'normal', 'normal'], '0:5', '2:3'),
        ('25003', [1, 3, 5, 7, 9], 'ABCDE', ['normal', 'normal', 'normal', 'normal', 'normal'], '5:0', '0:5'),
        ('25004', [2, 5, 3, 7, 3], 'ABCDC', ['normal', 'normal', 'repeat', 'normal', 'repeat'], '3:2', '3:2'),
        ('25005', [4, 4, 4, 5, 6], 'AAABC', ['repeat', 'repeat', 'repeat', 'normal', 'normal'], '1:4', '3:2'),
    ]
    
    for issue, numbers, format_chars, color_info, odd_even, big_small in sample_data:
        table.add_p5_record(issue, numbers, format_chars, color_info, odd_even, big_small)
    
    # 说明文本
    info_text = """
真彩色单元格说明：
• 淡蓝色背景 = 重复号码
• 爱丽丝蓝背景 = 奇数号码  
• 薰衣草红背景 = 偶数号码
• 黄色背景 = 高亮显示（点击切换）
• 点击单元格可以切换颜色状态
    """
    
    info_label = tk.Label(main_frame, text=info_text, justify=tk.LEFT, 
                         font=('Arial', 9), bg='#f0f0f0', padx=10, pady=10)
    info_label.pack(fill=tk.X, pady=10)
    
    root.mainloop()


if __name__ == "__main__":
    demo_cell_colored_table()
