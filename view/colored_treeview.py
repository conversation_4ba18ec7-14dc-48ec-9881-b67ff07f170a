"""
支持颜色显示的Treeview组件
用于在表格中显示带颜色的号码格式
"""
import tkinter as tk
from tkinter import ttk
from typing import Dict, List, Any


class ColoredTreeview(ttk.Treeview):
    """支持颜色显示的Treeview组件"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        # 配置颜色标签
        self.tag_configure('repeat_char', background='lightblue', foreground='darkblue')
        self.tag_configure('normal_char', background='white', foreground='black')
        
        # 存储颜色信息
        self.color_data = {}
    
    def insert_colored_row(self, parent, index, values, color_info=None, **kwargs):
        """
        插入带颜色信息的行
        
        Args:
            parent: 父节点
            index: 插入位置
            values: 行数据
            color_info: 颜色信息字典，格式如 {'column_index': ['normal', 'repeat', ...]}
            **kwargs: 其他参数
        """
        # 插入普通行
        item = self.insert(parent, index, values=values, **kwargs)
        
        # 如果有颜色信息，存储起来用于后续处理
        if color_info:
            self.color_data[item] = color_info
        
        return item
    
    def get_colored_format_display(self, format_chars: str, color_info: List[str]) -> str:
        """
        生成用于GUI显示的彩色格式字符串
        
        Args:
            format_chars: 格式字符串，如"AABBC"
            color_info: 颜色信息列表，如['normal', 'repeat', 'repeat', 'normal', 'normal']
            
        Returns:
            str: 用于显示的格式字符串（GUI中通过标签控制颜色）
        """
        # 在GUI中，我们仍然返回普通字符串，颜色通过其他方式控制
        return format_chars


def create_format_display_widget(parent, format_chars: str, color_info: List[str]) -> tk.Frame:
    """
    创建一个显示彩色号码格式的小部件
    
    Args:
        parent: 父容器
        format_chars: 格式字符串
        color_info: 颜色信息列表
        
    Returns:
        tk.Frame: 包含彩色格式显示的框架
    """
    frame = tk.Frame(parent)
    
    for i, (char, color) in enumerate(zip(format_chars, color_info)):
        if color == 'repeat':
            # 重复字符用淡蓝色显示
            label = tk.Label(frame, text=char, bg='lightblue', fg='darkblue', 
                           font=('Arial', 10, 'bold'), width=2)
        else:
            # 普通字符用默认颜色
            label = tk.Label(frame, text=char, bg='white', fg='black', 
                           font=('Arial', 10), width=2)
        
        label.pack(side=tk.LEFT, padx=1)
    
    return frame


def format_for_console_display(format_chars: str, color_info: List[str]) -> str:
    """
    生成用于控制台显示的彩色格式字符串
    
    Args:
        format_chars: 格式字符串
        color_info: 颜色信息列表
        
    Returns:
        str: 带ANSI颜色代码的字符串
    """
    result = ""
    for char, color in zip(format_chars, color_info):
        if color == 'repeat':
            # 淡蓝色显示重复字符
            result += f"\033[94m{char}\033[0m"
        else:
            result += char
    
    return result


def format_for_html_display(format_chars: str, color_info: List[str]) -> str:
    """
    生成用于HTML显示的彩色格式字符串
    
    Args:
        format_chars: 格式字符串
        color_info: 颜色信息列表
        
    Returns:
        str: 带HTML标签的字符串
    """
    result = ""
    for char, color in zip(format_chars, color_info):
        if color == 'repeat':
            # 淡蓝色显示重复字符
            result += f'<span style="color: darkblue; background-color: lightblue; font-weight: bold;">{char}</span>'
        else:
            result += char
    
    return result


class NumberFormatDisplay:
    """号码格式显示管理器"""
    
    @staticmethod
    def get_display_text(format_chars: str, color_info: List[str], display_type: str = 'plain') -> str:
        """
        根据显示类型获取格式化的文本
        
        Args:
            format_chars: 格式字符串
            color_info: 颜色信息列表
            display_type: 显示类型 ('plain', 'console', 'html')
            
        Returns:
            str: 格式化的显示文本
        """
        if display_type == 'console':
            return format_for_console_display(format_chars, color_info)
        elif display_type == 'html':
            return format_for_html_display(format_chars, color_info)
        else:
            # 普通文本显示，用符号标记重复字符
            result = ""
            for char, color in zip(format_chars, color_info):
                if color == 'repeat':
                    result += f"[{char}]"  # 用方括号标记重复字符
                else:
                    result += char
            return result
    
    @staticmethod
    def create_legend() -> str:
        """创建颜色图例说明"""
        return "格式说明：普通字符=单独号码，[蓝色字符]=重复号码"


def test_colored_format():
    """测试彩色格式显示"""
    print("=== 测试彩色号码格式显示 ===\n")
    
    # 测试数据
    test_cases = [
        ([2, 5, 3, 7, 3], "ABCDA"),  # 25373 -> A重复
        ([1, 1, 2, 3, 3], "AABCC"),  # 11233 -> A和C重复
        ([4, 4, 4, 5, 6], "AAABC"),  # 44456 -> A重复3次
        ([1, 2, 3, 4, 5], "ABCDE"),  # 12345 -> 无重复
        ([7, 7, 7, 7, 7], "AAAAA"),  # 77777 -> 全部重复
    ]
    
    for numbers, expected_format in test_cases:
        # 这里需要导入分析函数来测试
        print(f"号码: {numbers}")
        print(f"预期格式: {expected_format}")
        
        # 模拟颜色信息
        from collections import Counter
        counter = Counter(numbers)
        color_info = []
        format_chars = ""
        
        # 简化的格式生成逻辑
        char_map = {}
        char_index = 0
        chars = 'ABCDEFGHIJ'
        
        for num in numbers:
            if num not in char_map:
                char_map[num] = chars[char_index]
                char_index += 1
            
            format_chars += char_map[num]
            color_info.append('repeat' if counter[num] > 1 else 'normal')
        
        print(f"实际格式: {format_chars}")
        print(f"颜色信息: {color_info}")
        
        # 测试不同显示方式
        display_manager = NumberFormatDisplay()
        
        plain_text = display_manager.get_display_text(format_chars, color_info, 'plain')
        console_text = display_manager.get_display_text(format_chars, color_info, 'console')
        html_text = display_manager.get_display_text(format_chars, color_info, 'html')
        
        print(f"普通显示: {plain_text}")
        print(f"控制台显示: {console_text}")
        print(f"HTML显示: {html_text}")
        print("-" * 50)


if __name__ == "__main__":
    test_colored_format()
