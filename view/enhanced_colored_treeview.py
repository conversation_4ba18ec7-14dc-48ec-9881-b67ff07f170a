"""
增强版彩色Treeview组件
支持多种单元格属性触发方式
"""
import tkinter as tk
from tkinter import ttk
from typing import Dict, List, Any, Callable, Optional
import re


class EnhancedColoredTreeview(ttk.Treeview):
    """增强版支持颜色显示的Treeview组件"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        # 预定义颜色标签
        self.setup_color_tags()
        
        # 存储颜色规则和数据
        self.color_rules = {}
        self.cell_colors = {}
        self.row_colors = {}
        
        # 绑定事件
        self.bind('<Button-1>', self.on_click)
        self.bind('<Motion>', self.on_motion)
    
    def setup_color_tags(self):
        """设置预定义的颜色标签"""
        # 重复号码颜色
        self.tag_configure('repeat', background='lightblue', foreground='darkblue', font=('Arial', 9, 'bold'))
        
        # 正常号码颜色
        self.tag_configure('normal', background='white', foreground='black')
        
        # 高亮颜色
        self.tag_configure('highlight', background='yellow', foreground='black', font=('Arial', 9, 'bold'))
        
        # 警告颜色
        self.tag_configure('warning', background='orange', foreground='white', font=('Arial', 9, 'bold'))
        
        # 错误颜色
        self.tag_configure('error', background='red', foreground='white', font=('Arial', 9, 'bold'))
        
        # 成功颜色
        self.tag_configure('success', background='lightgreen', foreground='darkgreen', font=('Arial', 9, 'bold'))
        
        # 奇数颜色
        self.tag_configure('odd', background='#f0f8ff', foreground='blue')
        
        # 偶数颜色
        self.tag_configure('even', background='#fff0f5', foreground='purple')
        
        # 大数颜色
        self.tag_configure('big', background='#ffe4e1', foreground='red')
        
        # 小数颜色
        self.tag_configure('small', background='#e0ffe0', foreground='green')
    
    def add_color_rule(self, column: str, rule_func: Callable[[Any], str], rule_name: str = None):
        """
        添加颜色规则
        
        Args:
            column: 列名或列索引
            rule_func: 规则函数，接收单元格值，返回颜色标签名
            rule_name: 规则名称（可选）
        """
        if rule_name is None:
            rule_name = f"rule_{len(self.color_rules)}"
        
        self.color_rules[rule_name] = {
            'column': column,
            'function': rule_func
        }
    
    def insert_with_colors(self, parent, index, values, **kwargs):
        """
        插入带颜色的行
        
        Args:
            parent: 父节点
            index: 插入位置
            values: 行数据
            **kwargs: 其他参数，可包含 cell_colors, row_tag 等
        """
        # 插入行
        item = self.insert(parent, index, values=values)
        
        # 应用行级颜色
        if 'row_tag' in kwargs:
            self.item(item, tags=(kwargs['row_tag'],))
        
        # 应用单元格级颜色
        if 'cell_colors' in kwargs:
            self.cell_colors[item] = kwargs['cell_colors']
        
        # 应用颜色规则
        self.apply_color_rules(item, values)
        
        return item
    
    def apply_color_rules(self, item, values):
        """应用颜色规则"""
        if not self.color_rules:
            return
        
        # 获取列名
        columns = self['columns']
        
        for rule_name, rule_info in self.color_rules.items():
            column = rule_info['column']
            rule_func = rule_info['function']
            
            # 确定列索引
            if isinstance(column, str):
                if column in columns:
                    col_index = list(columns).index(column)
                else:
                    continue
            else:
                col_index = column
            
            # 检查索引有效性
            if 0 <= col_index < len(values):
                cell_value = values[col_index]
                color_tag = rule_func(cell_value)
                
                if color_tag:
                    # 存储单元格颜色信息
                    if item not in self.cell_colors:
                        self.cell_colors[item] = {}
                    self.cell_colors[item][col_index] = color_tag
    
    def update_cell_color(self, item, column, color_tag):
        """更新单元格颜色"""
        if item not in self.cell_colors:
            self.cell_colors[item] = {}
        
        # 确定列索引
        if isinstance(column, str):
            columns = self['columns']
            if column in columns:
                col_index = list(columns).index(column)
            else:
                return
        else:
            col_index = column
        
        self.cell_colors[item][col_index] = color_tag
        self.refresh_item_display(item)
    
    def refresh_item_display(self, item):
        """刷新项目显示（模拟单元格颜色）"""
        # 由于tkinter Treeview不支持真正的单元格颜色，
        # 我们通过修改显示文本来模拟颜色效果
        if item in self.cell_colors:
            values = list(self.item(item, 'values'))
            
            for col_index, color_tag in self.cell_colors[item].items():
                if 0 <= col_index < len(values):
                    original_value = str(values[col_index])
                    
                    # 根据颜色标签添加标记
                    if color_tag == 'repeat':
                        values[col_index] = f"[{original_value}]"
                    elif color_tag == 'highlight':
                        values[col_index] = f"★{original_value}★"
                    elif color_tag == 'warning':
                        values[col_index] = f"⚠{original_value}"
                    elif color_tag == 'error':
                        values[col_index] = f"❌{original_value}"
                    elif color_tag == 'success':
                        values[col_index] = f"✓{original_value}"
                    elif color_tag == 'odd':
                        values[col_index] = f"({original_value})"
                    elif color_tag == 'big':
                        values[col_index] = f"↑{original_value}"
                    elif color_tag == 'small':
                        values[col_index] = f"↓{original_value}"
            
            self.item(item, values=values)
    
    def on_click(self, event):
        """点击事件处理"""
        item = self.identify('item', event.x, event.y)
        column = self.identify('column', event.x, event.y)
        
        if item and column:
            # 可以在这里添加点击触发的颜色变化逻辑
            print(f"点击了项目 {item} 的列 {column}")
    
    def on_motion(self, event):
        """鼠标移动事件处理"""
        item = self.identify('item', event.x, event.y)
        column = self.identify('column', event.x, event.y)
        
        if item and column:
            # 可以在这里添加悬停效果
            pass


class NumberFormatColorizer:
    """号码格式着色器"""
    
    @staticmethod
    def create_repeat_rule():
        """创建重复号码规则"""
        def rule_func(value):
            # 检查是否包含重复标记
            if isinstance(value, str) and ('[' in value or ']' in value):
                return 'repeat'
            return None
        return rule_func
    
    @staticmethod
    def create_odd_even_rule():
        """创建奇偶规则"""
        def rule_func(value):
            try:
                num = int(str(value).strip('[]()★⚠❌✓↑↓'))
                return 'odd' if num % 2 == 1 else 'even'
            except:
                return None
        return rule_func
    
    @staticmethod
    def create_big_small_rule(threshold=5):
        """创建大小规则"""
        def rule_func(value):
            try:
                num = int(str(value).strip('[]()★⚠❌✓↑↓'))
                return 'big' if num >= threshold else 'small'
            except:
                return None
        return rule_func
    
    @staticmethod
    def create_range_rule(min_val, max_val, color_tag):
        """创建范围规则"""
        def rule_func(value):
            try:
                num = int(str(value).strip('[]()★⚠❌✓↑↓'))
                return color_tag if min_val <= num <= max_val else None
            except:
                return None
        return rule_func


def create_enhanced_p5_treeview(parent):
    """创建增强版排列五Treeview"""
    # 创建Treeview
    columns = ('期号', '号码1', '号码2', '号码3', '号码4', '号码5', '号码格式', '奇偶比', '大小比')
    tree = EnhancedColoredTreeview(parent, columns=columns, show='headings', height=15)
    
    # 设置列标题和宽度
    column_widths = {
        '期号': 80,
        '号码1': 50,
        '号码2': 50,
        '号码3': 50,
        '号码4': 50,
        '号码5': 50,
        '号码格式': 80,
        '奇偶比': 60,
        '大小比': 60
    }
    
    for col in columns:
        tree.heading(col, text=col)
        tree.column(col, width=column_widths.get(col, 80), anchor='center')
    
    # 添加颜色规则
    colorizer = NumberFormatColorizer()
    
    # 为号码列添加奇偶规则
    for i in range(1, 6):
        tree.add_color_rule(f'号码{i}', colorizer.create_odd_even_rule(), f'odd_even_{i}')
    
    # 为号码格式列添加重复规则
    tree.add_color_rule('号码格式', colorizer.create_repeat_rule(), 'repeat_format')
    
    return tree


def demo_enhanced_colored_treeview():
    """演示增强版彩色Treeview"""
    root = tk.Tk()
    root.title("增强版彩色Treeview演示")
    root.geometry("800x600")
    
    # 创建主框架
    main_frame = ttk.Frame(root, padding="10")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="排列五号码彩色显示演示", 
                           font=('Arial', 14, 'bold'))
    title_label.pack(pady=10)
    
    # 创建增强版Treeview
    tree = create_enhanced_p5_treeview(main_frame)
    tree.pack(fill=tk.BOTH, expand=True, pady=10)
    
    # 添加滚动条
    scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=tree.yview)
    tree.configure(yscrollcommand=scrollbar.set)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # 添加示例数据
    sample_data = [
        ('25001', 1, 1, 2, 3, 4, '[A][A]BCD', '3:2', '2:3'),  # 重复号码
        ('25002', 2, 4, 6, 8, 0, 'ABCDE', '0:5', '2:3'),      # 全偶数
        ('25003', 1, 3, 5, 7, 9, 'ABCDE', '5:0', '0:5'),      # 全奇数
        ('25004', 2, 5, 3, 7, 3, 'AB[C]D[C]', '3:2', '3:2'),  # 重复号码
        ('25005', 4, 4, 4, 5, 6, '[A][A][A]BC', '1:4', '3:2'), # 多重复
    ]
    
    for data in sample_data:
        tree.insert_with_colors('', tk.END, values=data)
    
    # 说明文本
    info_text = """
颜色说明：
• [方括号] = 重复号码（淡蓝色）
• (圆括号) = 奇数号码（蓝色）
• 无标记 = 偶数号码（紫色）
• ★星号★ = 高亮显示
• ⚠ = 警告
• ✓ = 成功
• ↑ = 大数
• ↓ = 小数
    """
    
    info_label = tk.Label(main_frame, text=info_text, justify=tk.LEFT, 
                         font=('Arial', 9), bg='#f0f0f0', padx=10, pady=10)
    info_label.pack(fill=tk.X, pady=10)
    
    # 控制按钮
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=5)
    
    def toggle_highlight():
        """切换高亮显示"""
        selected = tree.selection()
        if selected:
            item = selected[0]
            tree.update_cell_color(item, '号码格式', 'highlight')
    
    def reset_colors():
        """重置颜色"""
        for item in tree.get_children():
            if item in tree.cell_colors:
                del tree.cell_colors[item]
            tree.item(item, values=tree.item(item, 'values'))
    
    ttk.Button(button_frame, text="高亮选中项", command=toggle_highlight).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="重置颜色", command=reset_colors).pack(side=tk.LEFT, padx=5)
    
    root.mainloop()


if __name__ == "__main__":
    demo_enhanced_colored_treeview()
