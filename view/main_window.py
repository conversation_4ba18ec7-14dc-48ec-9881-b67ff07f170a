"""
主窗口界面 - 基于tkinter
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import pandas as pd
import os
from config.lottery_config import LotteryType, LotteryConfig
from view.colored_treeview import NumberFormatDisplay


class DataAnalysisThread(threading.Thread):
    """数据分析线程"""

    def __init__(self, controller, file_path, callback, is_import=False, lottery_type=None):
        super().__init__()
        self.controller = controller
        self.file_path = file_path
        self.callback = callback
        self.daemon = True
        self.is_import = is_import  # 是否是导入操作（重新分析）
        self.lottery_type = lottery_type  # 彩票类型

    def run(self):
        """运行数据分析"""
        try:
            # 更新进度
            self.callback("progress", 20)

            if self.is_import:
                # 导入并重新分析（重建数据库）
                lottery_name = LotteryConfig.get_name(self.lottery_type) if self.lottery_type else "数据"
                self.callback("status", f"正在导入{lottery_name}并重新分析...")

                # 更新进度
                self.callback("progress", 40)

                # 导入并分析
                analyzed_data = self.controller.import_and_analyze_data(self.file_path, self.lottery_type)
                if analyzed_data is None or len(analyzed_data) == 0:
                    self.callback("error", f"{lottery_name}导入和分析失败")
                    return

                # 更新进度
                self.callback("progress", 90)

                # 获取数据库统计信息
                stats = self.controller.get_database_statistics()
                self.callback("status", f"{lottery_name}数据库重建完成，共 {stats['total_records']} 条记录")

            else:
                # 普通加载数据
                self.callback("status", "正在加载数据...")

                # 加载数据
                success = self.controller.load_data(self.file_path)
                if not success:
                    self.callback("error", "数据加载失败")
                    return

                # 更新进度
                self.callback("progress", 50)

                # 分析数据
                self.callback("status", "正在分析数据...")
                analyzed_data = self.controller.analyze_data()
                if analyzed_data is None or len(analyzed_data) == 0:
                    self.callback("error", "数据分析失败")
                    return

            # 完成
            self.callback("progress", 100)
            self.callback("completed", analyzed_data)

        except Exception as e:
            self.callback("error", f"分析过程中出错: {str(e)}")


class MainWindow:
    """主窗口类"""

    def __init__(self, controller):
        self.controller = controller
        self.analysis_thread = None
        self.root = tk.Tk()
        self.init_ui()

        # 启动时检查是否有现有数据
        self.load_existing_data()

    def init_ui(self):
        """初始化用户界面"""
        self.root.title("大乐透数据分析系统")
        self.root.geometry("1400x800")

        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建顶部控制框架
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))

        # 左侧控制区域
        left_control = ttk.Frame(control_frame)
        left_control.pack(side=tk.LEFT)

        # 彩票类型选择
        type_frame = ttk.Frame(left_control)
        type_frame.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Label(type_frame, text="彩票类型:").pack(side=tk.LEFT, padx=(0, 5))

        self.lottery_type_var = tk.StringVar()
        self.lottery_type_combo = ttk.Combobox(
            type_frame,
            textvariable=self.lottery_type_var,
            values=LotteryConfig.get_type_names(),
            state="readonly",
            width=10
        )
        self.lottery_type_combo.pack(side=tk.LEFT)
        self.lottery_type_combo.set(LotteryConfig.get_name(LotteryType.DLT))  # 默认大乐透
        self.lottery_type_combo.bind('<<ComboboxSelected>>', self.on_lottery_type_changed)

        # 导入数据按钮（重新分析）
        self.import_button = ttk.Button(
            left_control,
            text="导入数据(重新分析)",
            command=self.import_data
        )
        self.import_button.pack(side=tk.LEFT, padx=(10, 0))

        # 右侧统计信息
        self.stats_label = ttk.Label(control_frame, text="数据统计: 未加载数据")
        self.stats_label.pack(side=tk.RIGHT)

        # 创建进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            main_frame,
            variable=self.progress_var,
            maximum=100
        )
        self.progress_bar.pack(fill=tk.X, pady=(0, 10))
        self.progress_bar.pack_forget()  # 初始隐藏

        # 创建数据表格
        self.create_table(main_frame)

        # 创建状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        self.status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
    
    def create_table(self, parent):
        """创建数据表格"""
        # 创建表格框架
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # 定义通用列标题（适用于所有彩票类型）
        self.columns = ['日期', '期号', '号码/红球', '连重/蓝球', '奇偶比', '奇偶排布',
                       '上次奇偶排布', '大小比/分区比', '大小排布/上次分区比', '间隔/格式', '格式/上次0分区比']

        # 创建Treeview表格
        self.table = ttk.Treeview(table_frame, columns=self.columns, show='headings', height=20)

        # 设置初始列标题
        self.update_table_headers(LotteryType.DLT)  # 默认大乐透

        # 设置列宽
        self.column_widths = {
            '日期': 100,
            '期号': 80,
            '号码/红球': 120,
            '连重/蓝球': 80,
            '奇偶比': 80,
            '奇偶排布': 100,
            '上次奇偶排布': 150,
            '大小比/分区比': 120,
            '大小排布/上次分区比': 150,
            '间隔/格式': 120,
            '格式/上次0分区比': 150
        }

        for col in self.columns:
            self.table.column(col, width=self.column_widths.get(col, 100), minwidth=50)

        # 创建滚动条
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.table.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.table.xview)

        self.table.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # 布局表格和滚动条
        self.table.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        # 配置网格权重
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

    def update_table_headers(self, lottery_type):
        """根据彩票类型更新表格标题"""
        if lottery_type == LotteryType.P5:
            # 排列五表格标题
            headers = ['日期', '期号', '号码', '连重', '奇偶比', '奇偶排布',
                      '上次奇偶排布', '大小比', '大小排布', '间隔', '号码格式']
        else:
            # 大乐透/双色球表格标题
            headers = ['日期', '期号', '红球', '蓝球', '奇偶比', '奇偶排布',
                      '上次奇偶排布', '分区比', '上次分区比', '间隔', '上次0分区比']

        # 更新列标题
        for i, header in enumerate(headers):
            if i < len(self.columns):
                self.table.heading(self.columns[i], text=header)
    
    def on_lottery_type_changed(self, event=None):
        """彩票类型改变处理"""
        selected_name = self.lottery_type_var.get()
        lottery_type = LotteryConfig.get_type_by_name(selected_name)

        # 更新表格标题
        self.update_table_headers(lottery_type)

        # 切换彩票类型并加载对应数据
        existing_data = self.controller.switch_lottery_type(lottery_type)

        if existing_data is not None:
            self.populate_table(existing_data)
            self.update_stats(len(existing_data))

            # 获取数据库统计信息
            stats = self.controller.get_database_statistics()
            lottery_name = LotteryConfig.get_name(lottery_type)
            self.status_var.set(f"已加载{lottery_name}数据库中的 {stats['total_records']} 条记录 "
                              f"(数据范围: {stats['start_date']} 至 {stats['end_date']})")
        else:
            # 清空表格
            for item in self.table.get_children():
                self.table.delete(item)

            lottery_name = LotteryConfig.get_name(lottery_type)
            self.update_stats(0)
            self.status_var.set(f"{lottery_name}数据库为空，请导入CSV文件进行分析")

    def load_existing_data(self):
        """启动时加载现有数据"""
        # 获取当前选择的彩票类型
        selected_name = self.lottery_type_var.get()
        lottery_type = LotteryConfig.get_type_by_name(selected_name)

        if self.controller.has_existing_data():
            # 切换到对应类型并加载数据
            existing_data = self.controller.switch_lottery_type(lottery_type)
            if existing_data is not None:
                self.populate_table(existing_data)
                self.update_stats(len(existing_data))

                # 获取数据库统计信息
                stats = self.controller.get_database_statistics()
                lottery_name = LotteryConfig.get_name(lottery_type)
                self.status_var.set(f"已加载{lottery_name}数据库中的 {stats['total_records']} 条记录 "
                                  f"(数据范围: {stats['start_date']} 至 {stats['end_date']})")
                return

        # 没有现有数据
        lottery_name = LotteryConfig.get_name(lottery_type)
        self.status_var.set(f"{lottery_name}数据库为空，请导入CSV文件进行分析")

    def import_data(self):
        """导入数据（重新分析）"""
        # 获取当前选择的彩票类型
        selected_name = self.lottery_type_var.get()
        lottery_type = LotteryConfig.get_type_by_name(selected_name)
        lottery_name = LotteryConfig.get_name(lottery_type)

        file_path = filedialog.askopenfilename(
            title=f"选择{lottery_name}CSV文件进行重新分析",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
        )

        if file_path:
            # 确认重新分析
            result = messagebox.askyesno(
                "确认重新分析",
                f"这将重新分析{lottery_name}数据并覆盖现有的数据库。\n确定要继续吗？"
            )
            if result:
                self.start_analysis(file_path, is_import=True, lottery_type=lottery_type)

    def start_analysis(self, file_path, is_import=False, lottery_type=None):
        """开始数据分析"""
        # 禁用导入按钮和彩票类型选择
        self.import_button.config(state='disabled')
        self.lottery_type_combo.config(state='disabled')

        # 显示进度条
        self.progress_bar.pack(fill=tk.X, pady=(0, 10))
        self.progress_var.set(0)

        # 更新状态
        lottery_name = LotteryConfig.get_name(lottery_type) if lottery_type else "数据"
        if is_import:
            self.status_var.set(f"正在重新分析{lottery_name}...")
        else:
            self.status_var.set(f"正在分析{lottery_name}...")

        # 创建并启动分析线程
        self.analysis_thread = DataAnalysisThread(
            self.controller,
            file_path,
            self.analysis_callback,
            is_import=is_import,
            lottery_type=lottery_type
        )
        self.analysis_thread.start()

    def analysis_callback(self, event_type, data):
        """分析回调函数"""
        if event_type == "progress":
            self.root.after(0, lambda: self.update_progress(data))
        elif event_type == "status":
            self.root.after(0, lambda: self.status_var.set(data))
        elif event_type == "completed":
            self.root.after(0, lambda: self.on_analysis_completed(data))
        elif event_type == "error":
            self.root.after(0, lambda: self.on_analysis_error(data))

    def update_progress(self, value):
        """更新进度条"""
        self.progress_var.set(value)

    def on_analysis_completed(self, analyzed_data):
        """分析完成处理"""
        # 获取当前彩票类型
        selected_name = self.lottery_type_var.get()
        lottery_type = LotteryConfig.get_type_by_name(selected_name)
        lottery_name = LotteryConfig.get_name(lottery_type)

        # 更新表格标题
        self.update_table_headers(lottery_type)

        # 填充表格数据
        self.populate_table(analyzed_data)

        # 隐藏进度条
        self.progress_bar.pack_forget()

        # 启用导入按钮和彩票类型选择
        self.import_button.config(state='normal')
        self.lottery_type_combo.config(state='readonly')

        # 更新统计信息
        self.update_stats(len(analyzed_data))

        # 更新状态
        self.status_var.set(f"{lottery_name}分析完成，共处理 {len(analyzed_data)} 条数据")

    def on_analysis_error(self, error_message):
        """分析错误处理"""
        # 隐藏进度条
        self.progress_bar.pack_forget()

        # 启用导入按钮和彩票类型选择
        self.import_button.config(state='normal')
        self.lottery_type_combo.config(state='readonly')

        # 显示错误消息
        messagebox.showerror("错误", error_message)

        # 更新状态
        self.status_var.set("分析失败")

    def update_stats(self, count):
        """更新数据统计"""
        self.stats_label.config(text=f"数据统计: 共 {count} 条记录")
    
    def populate_table(self, data):
        """填充表格数据（优化版本，支持分页和不同彩票类型）"""
        if data is None or len(data) == 0:
            return

        # 清空现有数据
        for item in self.table.get_children():
            self.table.delete(item)

        # 获取当前彩票类型
        selected_name = self.lottery_type_var.get()
        lottery_type = LotteryConfig.get_type_by_name(selected_name)

        # 限制显示的记录数量以避免界面卡顿
        max_display_records = 1000  # 最多显示1000条记录
        total_records = len(data)

        if total_records > max_display_records:
            # 显示最新的记录（数据已按日期降序排序，最新记录在前面）
            display_data = data.head(max_display_records)
            self.status_var.set(f"显示最新 {max_display_records} 条记录（共 {total_records} 条）")
        else:
            display_data = data

        # 根据彩票类型填充不同的列
        if lottery_type == LotteryType.P5:
            # 排列五数据格式
            for row_index, row_data in display_data.iterrows():
                # 构造号码显示格式
                numbers = f"{row_data['号码1']}{row_data['号码2']}{row_data['号码3']}{row_data['号码4']}{row_data['号码5']}"

                # 处理号码格式的彩色显示
                format_display = str(row_data['号码格式'])
                if '格式颜色' in row_data and row_data['格式颜色']:
                    # 使用普通文本显示，用符号标记重复字符
                    format_chars = str(row_data['号码格式'])
                    color_info = row_data['格式颜色'] if isinstance(row_data['格式颜色'], list) else ['normal'] * 5
                    format_display = NumberFormatDisplay.get_display_text(format_chars, color_info, 'plain')

                values = [
                    str(row_data['日期']),
                    str(row_data['期号']),
                    numbers,  # 红球位置显示号码
                    f"连{row_data['连号']}重{row_data['重号']}",  # 蓝球位置显示连号重号
                    str(row_data['奇偶比']),
                    str(row_data['奇偶排布']),
                    str(row_data['上次奇偶排布']),
                    str(row_data['大小比']),  # 分区比位置显示大小比
                    str(row_data['大小排布']),  # 上次分区比位置显示大小排布
                    str(row_data['间隔']),  # 间隔/格式位置显示间隔
                    format_display  # 格式/上次0分区比位置显示彩色号码格式
                ]
                self.table.insert('', 'end', values=values)
        else:
            # 大乐透/双色球数据格式
            for row_index, row_data in display_data.iterrows():
                values = [
                    str(row_data['日期']),
                    str(row_data['期号']),
                    str(row_data['红球']),
                    str(row_data['蓝球']),
                    str(row_data['奇偶比']),
                    str(row_data['奇偶排布']),
                    str(row_data['上次奇偶排布']),
                    str(row_data['分区比']),
                    str(row_data['上次分区比']),
                    "-",  # 间隔占位符（大乐透/双色球暂不支持）
                    str(row_data['上次0分区比'])
                ]
                self.table.insert('', 'end', values=values)

    def run(self):
        """运行主窗口"""
        self.root.mainloop()

    def destroy(self):
        """销毁窗口"""
        if self.analysis_thread and self.analysis_thread.is_alive():
            # 等待线程结束
            pass
        self.root.destroy()
