# 彩票数据分析Web应用

基于Flask的彩票数据分析系统，支持大乐透、双色球、排列五等多种彩票类型的数据分析。

## 🎯 功能特色

- **多彩票支持**: 大乐透、双色球、排列五
- **彩色显示**: 重复号码彩色标记，直观易懂
- **实时分析**: 输入号码即时获得分析结果
- **数据导入**: 支持CSV格式批量数据导入
- **响应式设计**: 完美适配桌面、平板、手机
- **专业分析**: 奇偶分析、分区分析、格式分析等

## 🚀 快速开始

### 1. 环境要求

- Python 3.7+
- 现代浏览器（Chrome、Firefox、Safari、Edge）

### 2. 安装依赖

```bash
# 进入webapp目录
cd webapp

# 安装Python依赖
pip install -r requirements.txt
```

### 3. 启动应用

```bash
# 方式1：使用启动脚本（推荐）
python run.py

# 方式2：直接启动Flask应用
python app.py
```

### 4. 访问应用

打开浏览器访问：http://127.0.0.1:5000

## 📊 使用指南

### 数据分析

1. 选择彩票类型（大乐透/双色球/排列五）
2. 查看历史开奖数据和分析结果
3. 使用表格的排序、搜索、分页功能

### 实时分析

1. 点击"实时分析"菜单
2. 选择彩票类型
3. 输入号码（用逗号分隔）
4. 点击"立即分析"获得结果

### 数据上传

1. 点击"数据上传"菜单
2. 选择彩票类型
3. 上传符合格式的CSV文件
4. 系统自动分析并入库

## 📁 CSV格式说明

### 大乐透格式
```csv
日期,期号,红球1,红球2,红球3,红球4,红球5,蓝球1,蓝球2
2024-01-01,24001,01,12,23,28,35,03,11
2024-01-03,24002,05,15,18,25,33,02,09
```

### 双色球格式
```csv
日期,期号,红球1,红球2,红球3,红球4,红球5,红球6,蓝球
2024-01-02,24001,01,05,12,18,25,33,08
2024-01-04,24002,03,09,15,21,28,31,12
```

### 排列五格式
```csv
日期,期号,号码1,号码2,号码3,号码4,号码5
2024-01-01,24001,1,2,3,4,5
2024-01-02,24002,6,7,8,9,0
```

## 🎨 界面特色

### 彩色号码显示
- 🔴 红色圆球：普通红球号码
- 🔵 蓝色圆球：蓝球号码
- 🟡 黄色圆球：重复号码（高亮显示）

### 响应式设计
- 📱 手机端：优化的触摸界面
- 💻 桌面端：完整功能体验
- 📟 平板端：平衡的显示效果

## 🔧 技术架构

### 后端技术
- **Flask**: 轻量级Web框架
- **SQLite**: 嵌入式数据库
- **Pandas**: 数据分析处理
- **Python**: 核心开发语言

### 前端技术
- **Bootstrap 5**: 响应式UI框架
- **DataTables**: 数据表格组件
- **jQuery**: JavaScript库
- **Font Awesome**: 图标字体

## 📈 分析功能

### 大乐透/双色球
- **奇偶分析**: 统计奇数偶数比例
- **分区分析**: 号码池分区分布
- **连号分析**: 相邻号码统计
- **重号分析**: 重复号码识别

### 排列五
- **奇偶分析**: 奇偶数比例统计
- **大小分析**: 大数小数分布
- **格式分析**: 号码格式模式
- **重复标记**: 彩色重复号码显示

## 🛠️ 开发说明

### 项目结构
```
webapp/
├── app.py              # Flask应用主文件
├── data_logic.py       # 数据逻辑层
├── requirements.txt    # 依赖包列表
├── run.py             # 启动脚本
├── README.md          # 说明文档
└── templates/         # HTML模板
    ├── base.html      # 基础模板
    ├── analysis.html  # 分析页面
    ├── realtime.html  # 实时分析
    ├── upload.html    # 数据上传
    ├── about.html     # 关于页面
    └── error.html     # 错误页面
```

### API接口
- `GET /analysis/<lottery_type>`: 获取分析页面
- `POST /api/analyze`: 实时号码分析
- `POST /api/upload`: CSV文件上传
- `GET /api/data/<lottery_type>`: 获取表格数据

### 扩展开发
1. 在`data_logic.py`中添加新的分析算法
2. 在模板中添加新的显示组件
3. 在`app.py`中添加新的路由处理

## ⚠️ 注意事项

1. **数据安全**: 本地运行，数据不会上传到外部服务器
2. **分析结果**: 仅供参考，彩票具有随机性
3. **理性购彩**: 请根据个人经济能力理性购彩
4. **浏览器兼容**: 建议使用现代浏览器获得最佳体验

## 🆘 故障排除

### 常见问题

**Q: 启动时提示端口被占用？**
A: 修改`app.py`中的端口号，或关闭占用5000端口的程序。

**Q: 上传CSV文件失败？**
A: 检查文件格式是否正确，确保是UTF-8编码的CSV文件。

**Q: 页面显示乱码？**
A: 确保浏览器编码设置为UTF-8。

**Q: 数据表格不显示？**
A: 检查是否已上传数据，或刷新页面重试。

### 技术支持

如遇到技术问题，请检查：
1. Python版本是否符合要求
2. 依赖包是否正确安装
3. 数据库文件是否有读写权限
4. 浏览器控制台是否有错误信息

## 📄 许可证

本项目仅供学习研究使用，请勿用于商业用途。

## 🎉 更新日志

### v1.0.0 (2024-01-01)
- ✨ 初始版本发布
- 🎯 支持大乐透、双色球、排列五分析
- 🌈 实现彩色号码显示
- 📱 响应式界面设计
- 📊 实时分析功能
- 📁 CSV数据导入功能
