#!/usr/bin/env python3
"""
彩票数据分析Web应用主文件
基于Flask框架，提供Web界面访问彩票分析功能
"""
from flask import Flask, render_template, request, redirect, url_for, jsonify, flash
import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from webapp.data_logic import (
    get_lottery_data, analyze_realtime_numbers, upload_csv_data,
    get_lottery_types, get_analysis_summary
)

app = Flask(__name__)
app.secret_key = 'lottery_analysis_secret_key_2024'

@app.route('/')
def index():
    """主页 - 重定向到大乐透分析"""
    return redirect(url_for('show_analysis', lottery_type='dlt'))

@app.route('/analysis/<lottery_type>')
def show_analysis(lottery_type):
    """显示彩票分析结果"""
    try:
        # 获取分析数据
        data = get_lottery_data(lottery_type)
        
        # 获取分析摘要
        summary = get_analysis_summary(lottery_type)
        
        # 获取彩票类型信息
        lottery_info = get_lottery_types().get(lottery_type, {})
        
        return render_template('analysis.html', 
                             lottery_data=data,
                             lottery_type=lottery_type,
                             lottery_info=lottery_info,
                             summary=summary)
    except Exception as e:
        flash(f'加载数据时出错: {str(e)}', 'error')
        return render_template('error.html', error=str(e))

@app.route('/realtime')
def realtime_analysis():
    """实时号码分析页面"""
    return render_template('realtime.html')

@app.route('/api/analyze', methods=['POST'])
def api_analyze():
    """API接口 - 实时分析号码"""
    try:
        data = request.get_json()
        lottery_type = data.get('lottery_type', 'dlt')
        red_balls = data.get('red_balls', [])
        blue_balls = data.get('blue_balls', [])
        
        # 验证输入
        if not red_balls or not blue_balls:
            return jsonify({'error': '请输入完整的号码'}), 400
        
        # 进行分析
        result = analyze_realtime_numbers(lottery_type, red_balls, blue_balls)
        
        return jsonify({
            'success': True,
            'result': result
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/upload')
def upload_page():
    """CSV上传页面"""
    return render_template('upload.html')

@app.route('/api/upload', methods=['POST'])
def api_upload():
    """API接口 - 上传CSV文件"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': '没有选择文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
        
        lottery_type = request.form.get('lottery_type', 'dlt')
        
        # 保存并处理文件
        result = upload_csv_data(file, lottery_type)
        
        if result['success']:
            flash(f'成功上传并分析了 {result["count"]} 条记录', 'success')
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/data/<lottery_type>')
def api_get_data(lottery_type):
    """API接口 - 获取彩票数据（用于DataTables）"""
    try:
        # 获取分页参数
        start = int(request.args.get('start', 0))
        length = int(request.args.get('length', 25))
        search_value = request.args.get('search[value]', '')
        
        # 获取数据
        data = get_lottery_data(lottery_type, start, length, search_value)
        
        return jsonify({
            'draw': int(request.args.get('draw', 1)),
            'recordsTotal': data['recordsTotal'],
            'recordsFiltered': data['recordsFiltered'],
            'data': data['data']
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/about')
def about():
    """关于页面"""
    return render_template('about.html')

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return render_template('error.html', 
                         error='页面未找到',
                         error_code=404), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    return render_template('error.html', 
                         error='服务器内部错误',
                         error_code=500), 500

# 自定义模板过滤器
@app.template_filter('format_numbers')
def format_numbers(numbers):
    """格式化号码显示"""
    if isinstance(numbers, list):
        return ' '.join([f'{num:02d}' for num in numbers])
    return str(numbers)

@app.template_filter('highlight_repeat')
def highlight_repeat(value, is_repeat):
    """高亮重复号码"""
    if is_repeat:
        return f'<span class="text-danger fw-bold">{value}</span>'
    return str(value)

if __name__ == '__main__':
    # 确保数据库存在
    from webapp.data_logic import init_database
    init_database()
    
    print("=" * 50)
    print("🎯 彩票数据分析Web应用启动中...")
    print("📊 支持大乐透、双色球、排列五分析")
    print("🌈 支持彩色号码显示")
    print("📱 响应式界面设计")
    print("🔗 访问地址: http://127.0.0.1:5000")
    print("=" * 50)
    
    # 启动Flask应用
    app.run(debug=True, host='127.0.0.1', port=5000)
