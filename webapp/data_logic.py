"""
Web应用数据逻辑层
处理所有数据分析、数据库交互等核心业务逻辑
"""
import os
import sys
import sqlite3
import pandas as pd
from typing import Dict, List, Any, Optional
import tempfile

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from config.lottery_config import LotteryType, LotteryConfig
from model.db_manager import SQLiteManager
from analysis.odd_even_analysis import analyze_odd_even_single
from analysis.zone_analysis import analyze_zone_single
from analysis.p5_analysis import analyze_p5_single_record, calculate_number_format_with_color
from controller.main_controller import MainController

# 使用基于当前文件位置的绝对路径，确保无论从哪里启动都能找到正确的数据库
def get_connection():
    """获取数据库连接"""
    # 获取当前文件所在目录的绝对路径
    basedir = os.path.abspath(os.path.dirname(__file__))
    # 从webapp目录回到项目根目录，然后进入data目录
    db_path = os.path.join(basedir, '..', 'data', 'analysis_results.db')
    # 规范化路径
    db_path = os.path.abspath(db_path)

    db_manager = SQLiteManager(db_path)
    return db_manager.get_connection()

def init_database():
    """初始化数据库"""
    try:
        # 数据库路径已修复，使用基于文件位置的绝对路径

        db_manager = SQLiteManager()
        # 确保数据库表存在
        conn = db_manager.get_connection()
        if conn:
            conn.close()
        return True
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        return False

def get_lottery_types():
    """获取支持的彩票类型"""
    return {
        'dlt': {
            'name': '大乐透',
            'description': '超级大乐透数据分析',
            'red_count': 5,
            'blue_count': 2,
            'red_range': (1, 35),
            'blue_range': (1, 12)
        },
        'ssq': {
            'name': '双色球',
            'description': '双色球数据分析',
            'red_count': 6,
            'blue_count': 1,
            'red_range': (1, 33),
            'blue_range': (1, 16)
        },
        'p5': {
            'name': '排列五',
            'description': '排列五数据分析',
            'red_count': 5,
            'blue_count': 0,
            'red_range': (0, 9),
            'blue_range': None
        }
    }

def get_lottery_data(lottery_type: str, start: int = 0, length: int = 25, search: str = '') -> Dict[str, Any]:
    """获取彩票数据 - 优化版本"""
    try:
        # 转换彩票类型
        if lottery_type == 'dlt':
            lt = LotteryType.DLT
        elif lottery_type == 'ssq':
            lt = LotteryType.SSQ
        elif lottery_type == 'p5':
            lt = LotteryType.P5
        else:
            raise ValueError(f"不支持的彩票类型: {lottery_type}")

        # 从数据库加载数据 - 使用正确的数据库路径
        basedir = os.path.abspath(os.path.dirname(__file__))
        db_path = os.path.abspath(os.path.join(basedir, '..', 'data', 'analysis_results.db'))
        db_manager = SQLiteManager(db_path)
        data = db_manager.load_results(lt)

        if data is None or len(data) == 0:
            return {
                'data': [],
                'recordsTotal': 0,
                'recordsFiltered': 0
            }

        # 数据已在数据库层按日期降序排序，无需重复排序

        # 搜索过滤（在分页前进行）
        filtered_data = data
        if search:
            # 简单的搜索实现 - 在期号和日期中搜索
            mask = (
                data['期号'].astype(str).str.contains(search, case=False, na=False) |
                data['日期'].astype(str).str.contains(search, case=False, na=False)
            )
            filtered_data = data[mask]

        total_records = len(data)
        filtered_records = len(filtered_data)

        # 分页处理
        if length > 0:
            page_data = filtered_data.iloc[start:start + length]
        else:
            page_data = filtered_data

        # 格式化当前页数据
        records = []
        for _, row in page_data.iterrows():
            try:
                record = format_record_for_web(row, lottery_type)
                records.append(record)
            except Exception as e:
                print(f"格式化记录失败: {e}")
                continue

        return {
            'data': records,
            'recordsTotal': total_records,
            'recordsFiltered': filtered_records
        }
        
    except Exception as e:
        print(f"获取数据失败: {e}")
        return {
            'records': [],
            'total': 0,
            'filtered': 0
        }

def format_record_for_web(row: pd.Series, lottery_type: str) -> Dict[str, Any]:
    """格式化记录用于Web显示"""
    record = {
        'date': str(row['日期']),
        'issue': str(row['期号']),
        'numbers': [],
        'analysis': {}
    }
    
    if lottery_type == 'p5':
        # 排列五格式
        numbers = [row['号码1'], row['号码2'], row['号码3'], row['号码4'], row['号码5']]
        
        # 获取颜色信息
        colors = row.get('格式颜色', ['normal'] * 5)
        if isinstance(colors, str):
            colors = colors.split(',') if colors else ['normal'] * 5
        
        for i, num in enumerate(numbers):
            record['numbers'].append({
                'value': num,
                'is_repeat': colors[i] == 'repeat' if i < len(colors) else False,
                'position': i + 1
            })
        
        record['analysis'] = {
            'format': row.get('号码格式', ''),
            'odd_even_ratio': row.get('奇偶比', ''),
            'big_small_ratio': row.get('大小比', ''),
            'consecutive': row.get('连号', 0),
            'repeat_count': row.get('重号', 0)
        }
        
    else:
        # 大乐透/双色球格式
        if lottery_type == 'dlt':
            red_count = 5
            blue_count = 2
        else:  # ssq
            red_count = 6
            blue_count = 1

        # 红球 - 从逗号分隔的字符串中解析
        red_balls_str = str(row.get('红球', ''))
        red_balls = []
        if red_balls_str and red_balls_str != 'nan':
            try:
                red_balls = [int(x.strip()) for x in red_balls_str.split(',')]
            except:
                red_balls = []

        # 蓝球 - 从逗号分隔的字符串中解析
        blue_balls_str = str(row.get('蓝球', ''))
        blue_balls = []
        if blue_balls_str and blue_balls_str != 'nan':
            try:
                blue_balls = [int(x.strip()) for x in blue_balls_str.split(',')]
            except:
                blue_balls = []
        
        record['red_balls'] = red_balls
        record['blue_balls'] = blue_balls
        
        record['analysis'] = {
            'odd_even_ratio': row.get('奇偶比', ''),
            'zone_ratio': row.get('分区比', '')
            # 大乐透和双色球不显示连号和重号
        }
    
    return record

def analyze_realtime_numbers(lottery_type: str, red_balls: List[int], blue_balls: List[int]) -> Dict[str, Any]:
    """实时分析号码"""
    try:
        if lottery_type == 'p5':
            # 排列五分析
            if len(red_balls) != 5:
                raise ValueError("排列五需要5个号码")
            
            result = analyze_p5_single_record(red_balls, LotteryType.P5)
            
            # 格式化结果
            format_info = calculate_number_format_with_color(red_balls)
            
            return {
                'lottery_type': lottery_type,
                'numbers': [{'value': num, 'is_repeat': format_info['colors'][i] == 'repeat'} 
                           for i, num in enumerate(red_balls)],
                'analysis': {
                    'format': result['号码格式'],
                    'odd_even_ratio': result['奇偶比'],
                    'big_small_ratio': result['大小比'],
                    'consecutive': result['连号'],
                    'repeat_count': result['重号']
                },
                'evaluation': generate_evaluation(result)
            }
        
        else:
            # 大乐透/双色球分析
            lt = LotteryType.DLT if lottery_type == 'dlt' else LotteryType.SSQ
            
            # 奇偶分析
            odd_even_result = analyze_odd_even_single(red_balls, lt)
            
            # 分区分析
            zone_result = analyze_zone_single(red_balls, lt)
            
            return {
                'lottery_type': lottery_type,
                'red_balls': red_balls,
                'blue_balls': blue_balls,
                'analysis': {
                    'odd_even_ratio': odd_even_result['奇偶比'],
                    'odd_even_pattern': odd_even_result['奇偶排布'],
                    'zone_ratio': zone_result['分区比'],
                    'zone_pattern': zone_result['分区排布']
                },
                'evaluation': generate_dlt_ssq_evaluation(odd_even_result, zone_result)
            }
            
    except Exception as e:
        raise Exception(f"分析失败: {str(e)}")

def generate_evaluation(result: Dict[str, Any]) -> List[str]:
    """生成排列五评估"""
    evaluation = []
    
    # 奇偶评估
    odd_even_ratio = result.get('奇偶比', '')
    if '3:2' in odd_even_ratio or '2:3' in odd_even_ratio:
        evaluation.append("✓ 奇偶比例较为均衡")
    else:
        evaluation.append("⚠ 奇偶比例需要关注")
    
    # 连号评估
    consecutive = result.get('连号', 0)
    if consecutive == 0:
        evaluation.append("✓ 无连续号码，分布较散")
    elif consecutive <= 2:
        evaluation.append("✓ 连续号码适中")
    else:
        evaluation.append("⚠ 连续号码较多")
    
    # 重号评估
    repeat_count = result.get('重号', 0)
    if repeat_count == 0:
        evaluation.append("✓ 无重复号码")
    else:
        evaluation.append(f"⚠ 有{repeat_count}个重复号码")
    
    return evaluation

def generate_dlt_ssq_evaluation(odd_even_result: Dict, zone_result: Dict) -> List[str]:
    """生成大乐透/双色球评估"""
    evaluation = []
    
    # 奇偶评估
    odd_even_ratio = odd_even_result.get('奇偶比', '')
    if '3:2' in odd_even_ratio or '2:3' in odd_even_ratio or '3:3' in odd_even_ratio:
        evaluation.append("✓ 奇偶比例较为均衡")
    else:
        evaluation.append("⚠ 奇偶比例偏向明显")
    
    # 分区评估
    zone_ratio = zone_result.get('分区比', '')
    if '2:2:1' in zone_ratio or '2:1:2' in zone_ratio or '1:2:2' in zone_ratio:
        evaluation.append("✓ 分区分布较为合理")
    else:
        evaluation.append("⚠ 分区分布需要关注")
    
    return evaluation

def get_analysis_summary(lottery_type: str) -> Dict[str, Any]:
    """获取分析摘要"""
    try:
        # 转换彩票类型
        if lottery_type == 'dlt':
            lt = LotteryType.DLT
        elif lottery_type == 'ssq':
            lt = LotteryType.SSQ
        elif lottery_type == 'p5':
            lt = LotteryType.P5
        else:
            return {}
        
        # 从数据库加载数据 - 使用正确的数据库路径
        basedir = os.path.abspath(os.path.dirname(__file__))
        db_path = os.path.abspath(os.path.join(basedir, '..', 'data', 'analysis_results.db'))
        db_manager = SQLiteManager(db_path)
        data = db_manager.load_results(lt)
        
        if data is None or len(data) == 0:
            return {}
        
        summary = {
            'total_records': len(data),
            'latest_issue': str(data.iloc[0]['期号']) if len(data) > 0 else '',
            'latest_date': str(data.iloc[0]['日期']) if len(data) > 0 else '',
        }
        
        if lottery_type == 'p5':
            # 排列五统计
            repeat_count = len(data[data['重号'] > 0]) if '重号' in data.columns else 0
            summary['repeat_rate'] = f"{repeat_count/len(data)*100:.1f}%" if len(data) > 0 else "0%"
        
        return summary
        
    except Exception as e:
        print(f"获取摘要失败: {e}")
        return {}

def upload_csv_data(file, lottery_type: str) -> Dict[str, Any]:
    """上传并处理CSV数据"""
    try:
        # 转换彩票类型
        if lottery_type == 'dlt':
            lt = LotteryType.DLT
        elif lottery_type == 'ssq':
            lt = LotteryType.SSQ
        elif lottery_type == 'p5':
            lt = LotteryType.P5
        else:
            raise ValueError(f"不支持的彩票类型: {lottery_type}")
        
        # 保存临时文件
        with tempfile.NamedTemporaryFile(mode='wb', suffix='.csv', delete=False) as tmp_file:
            file.save(tmp_file.name)
            temp_path = tmp_file.name
        
        try:
            # 使用控制器加载和分析数据
            controller = MainController()
            controller.set_lottery_type(lt)
            
            # 加载数据
            success = controller.load_data(temp_path, lt)
            if not success:
                raise Exception("数据加载失败")
            
            # 分析数据
            analyzed_data = controller.analyze_data()
            if analyzed_data is None:
                raise Exception("数据分析失败")
            
            return {
                'success': True,
                'count': len(analyzed_data),
                'message': f'成功处理 {len(analyzed_data)} 条记录'
            }
            
        finally:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.unlink(temp_path)
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }
