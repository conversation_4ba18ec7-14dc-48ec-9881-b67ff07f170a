#!/usr/bin/env python3
"""
彩票数据分析Web应用启动脚本
"""
import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from webapp.app import app
from webapp.data_logic import init_database

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 彩票数据分析Web应用")
    print("=" * 60)
    
    # 初始化数据库
    print("📊 初始化数据库...")
    if init_database():
        print("✅ 数据库初始化成功")
    else:
        print("❌ 数据库初始化失败")
        return
    
    print("\n🚀 启动Web服务器...")
    print("📱 功能特色:")
    print("   • 支持大乐透、双色球、排列五分析")
    print("   • 彩色号码显示，重复号码高亮")
    print("   • 实时号码分析功能")
    print("   • 响应式界面设计")
    print("   • CSV数据批量导入")
    
    print("\n🔗 访问地址:")
    print("   • 本地访问: http://127.0.0.1:5001")
    print("   • 局域网访问: http://[您的IP]:5001")
    
    print("\n📋 使用说明:")
    print("   1. 在浏览器中打开上述地址")
    print("   2. 选择彩票类型进行数据分析")
    print("   3. 使用实时分析功能输入号码")
    print("   4. 上传CSV文件进行批量分析")
    
    print("\n⚠️  注意事项:")
    print("   • 首次使用需要上传历史数据")
    print("   • 支持的CSV格式请参考系统说明")
    print("   • 分析结果仅供参考，请理性购彩")
    
    print("\n" + "=" * 60)
    print("🎉 系统启动完成，按 Ctrl+C 停止服务")
    print("=" * 60)
    
    try:
        # 启动Flask应用
        app.run(
            debug=False,  # 生产环境关闭调试模式
            host='0.0.0.0',  # 允许外部访问
            port=5001,  # 使用5001端口避免冲突
            threaded=True  # 支持多线程
        )
    except KeyboardInterrupt:
        print("\n\n👋 感谢使用彩票数据分析系统！")
        print("🎯 系统已安全关闭")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("💡 请检查端口5001是否被占用")

if __name__ == '__main__':
    main()
