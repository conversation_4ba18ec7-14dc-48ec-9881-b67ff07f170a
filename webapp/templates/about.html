{% extends "base.html" %}

{% block title %}关于系统 - 彩票分析系统{% endblock %}

{% block content %}
<div class="row">
    <!-- 页面标题 -->
    <div class="col-12">
        <div class="text-center mb-5">
            <h1>
                <i class="fas fa-chart-line text-primary me-3"></i>
                彩票数据分析系统
            </h1>
            <p class="lead text-muted">专业的彩票数据分析工具，助您洞察号码规律</p>
        </div>
    </div>
</div>

<!-- 系统介绍 -->
<div class="row mb-5">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-body text-center">
                <h3 class="card-title">
                    <i class="fas fa-bullseye text-success me-2"></i>
                    系统简介
                </h3>
                <p class="card-text">
                    本系统是一个基于Web的彩票数据分析平台，支持大乐透、双色球、排列五等多种彩票类型的数据分析。
                    通过专业的统计算法和直观的可视化界面，帮助用户深入了解彩票号码的分布规律和特征。
                </p>
            </div>
        </div>
    </div>
</div>

<!-- 功能特色 -->
<div class="row mb-5">
    <div class="col-12">
        <h3 class="text-center mb-4">
            <i class="fas fa-star text-warning me-2"></i>
            功能特色
        </h3>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100 lottery-card">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-chart-pie fa-3x text-primary"></i>
                </div>
                <h5 class="card-title">多维度分析</h5>
                <p class="card-text">
                    支持奇偶分析、分区分析、大小分析、连号分析等多种维度，
                    全方位解析号码特征和分布规律。
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100 lottery-card">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-palette fa-3x text-success"></i>
                </div>
                <h5 class="card-title">彩色可视化</h5>
                <p class="card-text">
                    重复号码彩色标记，直观显示号码特征，
                    让数据分析结果一目了然，提升用户体验。
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100 lottery-card">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-calculator fa-3x text-info"></i>
                </div>
                <h5 class="card-title">实时分析</h5>
                <p class="card-text">
                    支持实时号码输入和即时分析，
                    快速获得分析结果和专业评估建议。
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100 lottery-card">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-mobile-alt fa-3x text-warning"></i>
                </div>
                <h5 class="card-title">响应式设计</h5>
                <p class="card-text">
                    完美适配桌面、平板、手机等各种设备，
                    随时随地进行彩票数据分析。
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100 lottery-card">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-upload fa-3x text-danger"></i>
                </div>
                <h5 class="card-title">数据导入</h5>
                <p class="card-text">
                    支持CSV格式数据批量导入，
                    快速建立历史数据库，进行大规模分析。
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100 lottery-card">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-table fa-3x text-secondary"></i>
                </div>
                <h5 class="card-title">数据表格</h5>
                <p class="card-text">
                    强大的数据表格功能，支持排序、搜索、分页，
                    轻松浏览和查找历史开奖数据。
                </p>
            </div>
        </div>
    </div>
</div>

<!-- 支持的彩票类型 -->
<div class="row mb-5">
    <div class="col-12">
        <h3 class="text-center mb-4">
            <i class="fas fa-list me-2"></i>
            支持的彩票类型
        </h3>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-circle me-2"></i>大乐透
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>红球：5个号码 (1-35)</li>
                    <li><i class="fas fa-check text-success me-2"></i>蓝球：2个号码 (1-12)</li>
                    <li><i class="fas fa-check text-success me-2"></i>奇偶分析、分区分析</li>
                    <li><i class="fas fa-check text-success me-2"></i>连号分析、重号分析</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card border-primary">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-circle me-2"></i>双色球
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>红球：6个号码 (1-33)</li>
                    <li><i class="fas fa-check text-success me-2"></i>蓝球：1个号码 (1-16)</li>
                    <li><i class="fas fa-check text-success me-2"></i>奇偶分析、分区分析</li>
                    <li><i class="fas fa-check text-success me-2"></i>连号分析、重号分析</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card border-success">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-circle me-2"></i>排列五
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>号码：5个数字 (0-9)</li>
                    <li><i class="fas fa-check text-success me-2"></i>奇偶分析、大小分析</li>
                    <li><i class="fas fa-check text-success me-2"></i>号码格式、重复标记</li>
                    <li><i class="fas fa-check text-success me-2"></i>位置间隔分析</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- 技术架构 -->
<div class="row mb-5">
    <div class="col-12">
        <div class="card analysis-card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cogs me-2"></i>技术架构
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-server me-2"></i>后端技术</h6>
                        <ul>
                            <li><strong>Flask</strong> - 轻量级Web框架</li>
                            <li><strong>SQLite</strong> - 嵌入式数据库</li>
                            <li><strong>Pandas</strong> - 数据分析库</li>
                            <li><strong>Python</strong> - 核心开发语言</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-desktop me-2"></i>前端技术</h6>
                        <ul>
                            <li><strong>Bootstrap 5</strong> - 响应式UI框架</li>
                            <li><strong>DataTables</strong> - 数据表格组件</li>
                            <li><strong>jQuery</strong> - JavaScript库</li>
                            <li><strong>Font Awesome</strong> - 图标字体</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 使用说明 -->
<div class="row mb-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-question-circle me-2"></i>使用说明
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>1. 数据分析</h6>
                        <p>选择彩票类型，查看历史开奖数据和分析结果。支持排序、搜索、分页等功能。</p>
                        
                        <h6>2. 实时分析</h6>
                        <p>输入号码进行即时分析，获得奇偶比、分区比等专业分析结果和评估建议。</p>
                    </div>
                    <div class="col-md-6">
                        <h6>3. 数据上传</h6>
                        <p>上传CSV格式的历史数据文件，系统自动进行批量分析和入库。</p>
                        
                        <h6>4. 彩色显示</h6>
                        <p>重复号码用特殊颜色标记，让数据特征更加直观易懂。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 免责声明 -->
<div class="row">
    <div class="col-12">
        <div class="alert alert-warning">
            <h5><i class="fas fa-exclamation-triangle me-2"></i>免责声明</h5>
            <p class="mb-0">
                本系统仅供学习研究和数据分析使用，所有分析结果仅供参考。
                彩票开奖具有随机性，任何分析方法都无法保证中奖。
                请理性购彩，量力而行，切勿沉迷。
            </p>
        </div>
    </div>
</div>
{% endblock %}
