{% extends "base.html" %}

{% block title %}{{ lottery_info.name }}数据分析 - 彩票分析系统{% endblock %}

{% block content %}
<div class="row">
    <!-- 页面标题 -->
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2>
                    <i class="fas fa-chart-bar text-primary me-2"></i>
                    {{ lottery_info.name }}数据分析
                </h2>
                <p class="text-muted mb-0">{{ lottery_info.description }}</p>
            </div>
            <div class="text-end">
                <a href="{{ url_for('realtime_analysis') }}" class="btn btn-lottery">
                    <i class="fas fa-calculator me-2"></i>实时分析
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 数据摘要 -->
{% if summary %}
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card summary-card">
            <div class="card-body text-center">
                <h5 class="card-title">
                    <i class="fas fa-database me-2"></i>总记录数
                </h5>
                <h3 class="mb-0">{{ summary.total_records }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card summary-card">
            <div class="card-body text-center">
                <h5 class="card-title">
                    <i class="fas fa-calendar me-2"></i>最新期号
                </h5>
                <h3 class="mb-0">{{ summary.latest_issue }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card summary-card">
            <div class="card-body text-center">
                <h5 class="card-title">
                    <i class="fas fa-clock me-2"></i>最新日期
                </h5>
                <h3 class="mb-0">{{ summary.latest_date }}</h3>
            </div>
        </div>
    </div>
    {% if summary.repeat_rate %}
    <div class="col-md-3">
        <div class="card summary-card">
            <div class="card-body text-center">
                <h5 class="card-title">
                    <i class="fas fa-percentage me-2"></i>重复率
                </h5>
                <h3 class="mb-0">{{ summary.repeat_rate }}</h3>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endif %}

<!-- 数据表格 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-table me-2"></i>历史开奖数据
                </h5>
            </div>
            <div class="card-body">
                <!-- 颜色说明 -->
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>颜色说明：</strong>
                    <span class="number-ball repeat-number ms-2">重复号码</span>
                    <span class="number-ball red-ball ms-2">普通号码</span>
                    {% if lottery_type != 'p5' %}
                    <span class="number-ball blue-ball ms-2">蓝球</span>
                    {% endif %}
                </div>



                <div class="table-responsive">
                    <table id="lotteryTable" class="table table-striped table-bordered" style="width:100%">
                        <!-- DataTables将自动生成表头和表体 -->
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 分析说明 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card analysis-card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i>分析说明
                </h5>
            </div>
            <div class="card-body">
                {% if lottery_type == 'p5' %}
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-chart-pie me-2"></i>奇偶分析</h6>
                        <p>统计5个号码中奇数和偶数的比例，理想比例为3:2或2:3。</p>
                        
                        <h6><i class="fas fa-arrows-alt me-2"></i>大小分析</h6>
                        <p>以5为界限，统计大数(5-9)和小数(0-4)的比例。</p>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-link me-2"></i>连号分析</h6>
                        <p>统计相邻号码的数量，连号过多可能影响中奖概率。</p>
                        
                        <h6><i class="fas fa-copy me-2"></i>重号分析</h6>
                        <p>统计重复出现的号码，重复号码用黄色高亮显示。</p>
                    </div>
                </div>
                {% else %}
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-chart-pie me-2"></i>奇偶分析</h6>
                        <p>统计红球中奇数和偶数的比例，均衡的奇偶比例有助于提高中奖概率。</p>
                        
                        <h6><i class="fas fa-th me-2"></i>分区分析</h6>
                        <p>将号码池分为若干区域，分析号码在各区域的分布情况。</p>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-link me-2"></i>连号分析</h6>
                        <p>统计相邻号码的数量，适度的连号有助于号码组合的合理性。</p>
                        
                        <h6><i class="fas fa-copy me-2"></i>重号分析</h6>
                        <p>分析与上期开奖号码的重复情况，重号是常见的开奖特征。</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // 高亮排列五格式字符串中的重复字符
    function highlightP5Format(formatStr) {
        if (!formatStr) {
            return '';
        }

        // 统计每个字符的出现次数
        const counts = {};
        for (const char of formatStr) {
            counts[char] = (counts[char] || 0) + 1;
        }

        // 为重复字符添加高亮样式
        let resultHtml = '';
        for (const char of formatStr) {
            if (counts[char] > 1) {
                resultHtml += `<span class="highlight-repeat-char">${char}</span>`;
            } else {
                resultHtml += char;
            }
        }
        return resultHtml;
    }

    // 初始化DataTables
    var table = $('#lotteryTable').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "{{ url_for('api_get_data', lottery_type=lottery_type) }}",
            "type": "GET",
            "dataSrc": "data"
        },
        "columns": [
            { "data": "date", "title": "日期" },
            { "data": "issue", "title": "期号" },
            {% if lottery_type == 'p5' %}
                { "data": "numbers.0", "title": "万位", "render": function(data, type, row) {
                    return formatNumber(data.value, data.is_repeat);
                }},
                { "data": "numbers.1", "title": "千位", "render": function(data, type, row) {
                    return formatNumber(data.value, data.is_repeat);
                }},
                { "data": "numbers.2", "title": "百位", "render": function(data, type, row) {
                    return formatNumber(data.value, data.is_repeat);
                }},
                { "data": "numbers.3", "title": "十位", "render": function(data, type, row) {
                    return formatNumber(data.value, data.is_repeat);
                }},
                { "data": "numbers.4", "title": "个位", "render": function(data, type, row) {
                    return formatNumber(data.value, data.is_repeat);
                }},
                {
                    "data": "analysis.format",
                    "title": "格式",
                    "render": function(data, type, row) {
                        if (type === 'display') {
                            return highlightP5Format(data);
                        }
                        return data;
                    }
                },
                { "data": "analysis.odd_even_ratio", "title": "奇偶比" },
                { "data": "analysis.big_small_ratio", "title": "大小比" },
                { "data": "analysis.consecutive", "title": "连号" },
                { "data": "analysis.repeat_count", "title": "重号" }
            {% elif lottery_type == 'dlt' %}
                {
                    "data": "red_balls",
                    "title": "红球",
                    "render": function(data, type, row) {
                        if (!data || !Array.isArray(data)) return '';
                        return data.map(num => `<span class="number-ball red-ball">${String(num).padStart(2, '0')}</span>`).join(' ');
                    },
                    "orderable": false
                },
                {
                    "data": "blue_balls",
                    "title": "蓝球",
                    "render": function(data, type, row) {
                        if (!data || !Array.isArray(data)) return '';
                        return data.map(num => `<span class="number-ball blue-ball">${String(num).padStart(2, '0')}</span>`).join(' ');
                    },
                    "orderable": false
                },
                { "data": "analysis.odd_even_ratio", "title": "奇偶比" },
                { "data": "analysis.zone_ratio", "title": "分区比" }
            {% else %}
                {
                    "data": "red_balls",
                    "title": "红球",
                    "render": function(data, type, row) {
                        if (!data || !Array.isArray(data)) return '';
                        return data.map(num => `<span class="number-ball red-ball">${String(num).padStart(2, '0')}</span>`).join(' ');
                    },
                    "orderable": false
                },
                {
                    "data": "blue_balls",
                    "title": "蓝球",
                    "render": function(data, type, row) {
                        if (!data || !Array.isArray(data)) return '';
                        return data.map(num => `<span class="number-ball blue-ball">${String(num).padStart(2, '0')}</span>`).join(' ');
                    },
                    "orderable": false
                },
                { "data": "analysis.odd_even_ratio", "title": "奇偶比" },
                { "data": "analysis.zone_ratio", "title": "分区比" }
            {% endif %}
        ],
        "order": [[ 1, "desc" ]],
        "pageLength": 25,
        "language": {
            "processing": "数据加载中...",
            "search": "搜索:",
            "lengthMenu": "显示 _MENU_ 条记录",
            "info": "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
            "paginate": {
                "previous": "上页",
                "next": "下页"
            }
        },
        "initComplete": function(settings, json) {
            console.log('DataTables 初始化完成，记录数:', json ? json.recordsTotal : 0);
        }
    });
});
</script>
{% endblock %}
