<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}彩票数据分析系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- 自定义样式 -->
    <style>
        .navbar-brand {
            font-weight: bold;
        }
        
        .lottery-card {
            transition: transform 0.2s;
        }
        
        .lottery-card:hover {
            transform: translateY(-2px);
        }
        
        .number-ball {
            display: inline-block;
            width: 35px;
            height: 35px;
            line-height: 35px;
            text-align: center;
            border-radius: 50%;
            margin: 2px;
            font-weight: bold;
            color: white;
        }
        
        .red-ball {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
        }
        
        .blue-ball {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
        }
        
        .repeat-number {
            background: linear-gradient(45deg, #ffd93d, #ff9f43) !important;
            color: #333 !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        /* 排列五格式中重复字符的淡蓝色高亮样式 */
        .highlight-repeat-char {
            background-color: #e0f7ff;
            color: #0066cc;
            font-weight: bold;
            padding: 2px 4px;
            border-radius: 3px;
            margin: 0 1px;
        }
        
        .analysis-card {
            border-left: 4px solid #007bff;
        }
        
        .summary-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .footer {
            background-color: #343a40;
            color: white;
            padding: 20px 0;
            margin-top: 50px;
        }
        
        .realtime-form {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }
        
        .evaluation-item {
            padding: 8px 12px;
            margin: 4px 0;
            border-radius: 5px;
            background: #f8f9fa;
        }
        
        .evaluation-success {
            background: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }
        
        .evaluation-warning {
            background: #fff3cd;
            color: #856404;
            border-left: 4px solid #ffc107;
        }
        
        .loading {
            display: none;
        }
        
        .btn-lottery {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
        }
        
        .btn-lottery:hover {
            background: linear-gradient(45deg, #764ba2, #667eea);
            color: white;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-chart-line me-2"></i>彩票数据分析系统
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-chart-bar me-1"></i>数据分析
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('show_analysis', lottery_type='dlt') }}">
                                <i class="fas fa-circle text-danger me-2"></i>大乐透
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('show_analysis', lottery_type='ssq') }}">
                                <i class="fas fa-circle text-primary me-2"></i>双色球
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('show_analysis', lottery_type='p5') }}">
                                <i class="fas fa-circle text-success me-2"></i>排列五
                            </a></li>
                        </ul>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('realtime_analysis') }}">
                            <i class="fas fa-calculator me-1"></i>实时分析
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('upload_page') }}">
                            <i class="fas fa-upload me-1"></i>数据上传
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('about') }}">
                            <i class="fas fa-info-circle me-1"></i>关于
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container my-4">
        <!-- Flash消息 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show" role="alert">
                        <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }} me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        {% block content %}{% endblock %}
    </main>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-chart-line me-2"></i>彩票数据分析系统</h5>
                    <p class="mb-0">专业的彩票数据分析工具，支持大乐透、双色球、排列五等多种彩票类型。</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <h6>功能特色</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>实时号码分析</li>
                        <li><i class="fas fa-check text-success me-2"></i>彩色号码显示</li>
                        <li><i class="fas fa-check text-success me-2"></i>数据可视化</li>
                        <li><i class="fas fa-check text-success me-2"></i>响应式设计</li>
                    </ul>
                </div>
            </div>
            <hr class="my-3">
            <div class="row">
                <div class="col-12 text-center">
                    <small>&copy; 2024 彩票数据分析系统. 仅供学习研究使用.</small>
                </div>
            </div>
        </div>
    </footer>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    
    <!-- 公共JavaScript -->
    <script>
        // 全局配置
        $(document).ready(function() {
            // 初始化工具提示
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
            
            // 自动隐藏alert
            setTimeout(function() {
                $('.alert').fadeOut();
            }, 5000);
        });
        
        // 格式化号码显示
        function formatNumber(num, isRepeat = false) {
            const formatted = String(num); // 移除0填充，直接显示原始数字
            if (isRepeat) {
                return `<span class="number-ball repeat-number">${formatted}</span>`;
            }
            return `<span class="number-ball red-ball">${formatted}</span>`;
        }
        
        // 显示加载状态
        function showLoading(element) {
            $(element).prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>处理中...');
        }
        
        // 隐藏加载状态
        function hideLoading(element, originalText) {
            $(element).prop('disabled', false).html(originalText);
        }
        
        // 显示错误消息
        function showError(message) {
            const alert = `
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('.container').prepend(alert);
        }
        
        // 显示成功消息
        function showSuccess(message) {
            const alert = `
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('.container').prepend(alert);
        }
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
