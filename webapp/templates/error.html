{% extends "base.html" %}

{% block title %}
{% if error_code %}
错误 {{ error_code }}
{% else %}
系统错误
{% endif %}
- 彩票分析系统
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-6 mx-auto">
        <div class="text-center">
            <!-- 错误图标 -->
            <div class="mb-4">
                {% if error_code == 404 %}
                    <i class="fas fa-search fa-5x text-warning"></i>
                {% else %}
                    <i class="fas fa-exclamation-triangle fa-5x text-danger"></i>
                {% endif %}
            </div>
            
            <!-- 错误标题 -->
            <h1 class="display-4 mb-3">
                {% if error_code == 404 %}
                    页面未找到
                {% elif error_code == 500 %}
                    服务器错误
                {% else %}
                    系统错误
                {% endif %}
            </h1>
            
            <!-- 错误代码 -->
            {% if error_code %}
            <h2 class="text-muted mb-4">错误代码: {{ error_code }}</h2>
            {% endif %}
            
            <!-- 错误描述 -->
            <div class="alert alert-{{ 'warning' if error_code == 404 else 'danger' }} mb-4">
                <h5 class="alert-heading">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if error_code == 404 %}
                        页面不存在
                    {% elif error_code == 500 %}
                        服务器内部错误
                    {% else %}
                        发生了错误
                    {% endif %}
                </h5>
                <p class="mb-0">
                    {% if error %}
                        {{ error }}
                    {% elif error_code == 404 %}
                        您访问的页面不存在，可能已被删除或移动。
                    {% elif error_code == 500 %}
                        服务器遇到了内部错误，无法完成您的请求。
                    {% else %}
                        系统遇到了未知错误，请稍后重试。
                    {% endif %}
                </p>
            </div>
            
            <!-- 解决建议 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-lightbulb me-2"></i>解决建议
                    </h5>
                </div>
                <div class="card-body text-start">
                    {% if error_code == 404 %}
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>检查URL地址是否正确</li>
                        <li><i class="fas fa-check text-success me-2"></i>使用导航菜单访问其他页面</li>
                        <li><i class="fas fa-check text-success me-2"></i>返回首页重新开始</li>
                        <li><i class="fas fa-check text-success me-2"></i>联系管理员报告问题</li>
                    </ul>
                    {% elif error_code == 500 %}
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>刷新页面重新尝试</li>
                        <li><i class="fas fa-check text-success me-2"></i>稍后再次访问</li>
                        <li><i class="fas fa-check text-success me-2"></i>清除浏览器缓存</li>
                        <li><i class="fas fa-check text-success me-2"></i>联系技术支持</li>
                    </ul>
                    {% else %}
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>刷新页面重新尝试</li>
                        <li><i class="fas fa-check text-success me-2"></i>检查网络连接</li>
                        <li><i class="fas fa-check text-success me-2"></i>返回上一页</li>
                        <li><i class="fas fa-check text-success me-2"></i>联系技术支持</li>
                    </ul>
                    {% endif %}
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="d-grid gap-2 d-md-block">
                <a href="{{ url_for('index') }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-home me-2"></i>返回首页
                </a>
                
                <button onclick="history.back()" class="btn btn-secondary btn-lg">
                    <i class="fas fa-arrow-left me-2"></i>返回上页
                </button>
                
                <button onclick="location.reload()" class="btn btn-info btn-lg">
                    <i class="fas fa-redo me-2"></i>刷新页面
                </button>
            </div>
            
            <!-- 快速导航 -->
            <div class="mt-5">
                <h5>快速导航</h5>
                <div class="row">
                    <div class="col-md-4">
                        <a href="{{ url_for('show_analysis', lottery_type='dlt') }}" class="btn btn-outline-danger w-100 mb-2">
                            <i class="fas fa-chart-bar me-2"></i>大乐透分析
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="{{ url_for('show_analysis', lottery_type='ssq') }}" class="btn btn-outline-primary w-100 mb-2">
                            <i class="fas fa-chart-pie me-2"></i>双色球分析
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="{{ url_for('show_analysis', lottery_type='p5') }}" class="btn btn-outline-success w-100 mb-2">
                            <i class="fas fa-chart-line me-2"></i>排列五分析
                        </a>
                    </div>
                </div>
                
                <div class="row mt-2">
                    <div class="col-md-6">
                        <a href="{{ url_for('realtime_analysis') }}" class="btn btn-outline-info w-100 mb-2">
                            <i class="fas fa-calculator me-2"></i>实时分析
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="{{ url_for('upload_page') }}" class="btn btn-outline-warning w-100 mb-2">
                            <i class="fas fa-upload me-2"></i>数据上传
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 错误详情（仅在调试模式下显示） -->
{% if config.DEBUG and error %}
<div class="row mt-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bug me-2"></i>错误详情 (调试信息)
                </h5>
            </div>
            <div class="card-body">
                <pre class="bg-light p-3 rounded"><code>{{ error }}</code></pre>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // 自动刷新倒计时（仅对500错误）
    {% if error_code == 500 %}
    let countdown = 30;
    const refreshBtn = $('button:contains("刷新页面")');
    
    function updateCountdown() {
        if (countdown > 0) {
            refreshBtn.html(`<i class="fas fa-redo me-2"></i>刷新页面 (${countdown}s)`);
            countdown--;
            setTimeout(updateCountdown, 1000);
        } else {
            location.reload();
        }
    }
    
    // 开始倒计时
    setTimeout(updateCountdown, 5000);
    {% endif %}
    
    // 记录错误信息（用于统计）
    if (typeof gtag !== 'undefined') {
        gtag('event', 'exception', {
            'description': '{{ error_code or "unknown" }}: {{ error or "Unknown error" }}',
            'fatal': false
        });
    }
});
</script>
{% endblock %}
