{% extends "base.html" %}

{% block title %}实时号码分析 - 彩票分析系统{% endblock %}

{% block content %}
<div class="row">
    <!-- 页面标题 -->
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2>
                    <i class="fas fa-calculator text-success me-2"></i>
                    实时号码分析
                </h2>
                <p class="text-muted mb-0">输入号码进行即时分析，支持大乐透、双色球、排列五</p>
            </div>
        </div>
    </div>
</div>

<!-- 分析表单 -->
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>号码输入
                </h5>
            </div>
            <div class="card-body realtime-form">
                <form id="analysisForm">
                    <!-- 彩票类型选择 -->
                    <div class="mb-4">
                        <label class="form-label">选择彩票类型</label>
                        <div class="btn-group w-100" role="group">
                            <input type="radio" class="btn-check" name="lottery_type" id="dlt" value="dlt" checked>
                            <label class="btn btn-outline-primary" for="dlt">
                                <i class="fas fa-circle text-danger me-2"></i>大乐透
                            </label>
                            
                            <input type="radio" class="btn-check" name="lottery_type" id="ssq" value="ssq">
                            <label class="btn btn-outline-primary" for="ssq">
                                <i class="fas fa-circle text-primary me-2"></i>双色球
                            </label>
                            
                            <input type="radio" class="btn-check" name="lottery_type" id="p5" value="p5">
                            <label class="btn btn-outline-primary" for="p5">
                                <i class="fas fa-circle text-success me-2"></i>排列五
                            </label>
                        </div>
                    </div>
                    
                    <!-- 大乐透输入 -->
                    <div id="dlt-input" class="lottery-input">
                        <div class="row">
                            <div class="col-md-8">
                                <label class="form-label">红球号码 (5个，范围1-35)</label>
                                <input type="text" class="form-control" id="dlt-red" placeholder="例如：01,12,23,28,35">
                                <div class="form-text">请输入5个红球号码，用逗号分隔</div>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">蓝球号码 (2个，范围1-12)</label>
                                <input type="text" class="form-control" id="dlt-blue" placeholder="例如：03,11">
                                <div class="form-text">请输入2个蓝球号码</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 双色球输入 -->
                    <div id="ssq-input" class="lottery-input" style="display: none;">
                        <div class="row">
                            <div class="col-md-8">
                                <label class="form-label">红球号码 (6个，范围1-33)</label>
                                <input type="text" class="form-control" id="ssq-red" placeholder="例如：01,05,12,18,25,33">
                                <div class="form-text">请输入6个红球号码，用逗号分隔</div>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">蓝球号码 (1个，范围1-16)</label>
                                <input type="text" class="form-control" id="ssq-blue" placeholder="例如：08">
                                <div class="form-text">请输入1个蓝球号码</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 排列五输入 -->
                    <div id="p5-input" class="lottery-input" style="display: none;">
                        <div class="row">
                            <div class="col-md-8">
                                <label class="form-label">号码 (5个，范围0-9)</label>
                                <input type="text" class="form-control" id="p5-numbers" placeholder="例如：1,2,3,4,5">
                                <div class="form-text">请输入5个号码，用逗号分隔</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 分析按钮 -->
                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-lottery btn-lg">
                            <i class="fas fa-chart-line me-2"></i>立即分析
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 分析结果 -->
<div class="row mt-4" id="resultSection" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>分析结果
                </h5>
            </div>
            <div class="card-body">
                <div id="analysisResult">
                    <!-- 分析结果将在这里显示 -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 使用说明 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card analysis-card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-question-circle me-2"></i>使用说明
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6><i class="fas fa-circle text-danger me-2"></i>大乐透</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>红球：5个号码，范围1-35</li>
                            <li><i class="fas fa-check text-success me-2"></i>蓝球：2个号码，范围1-12</li>
                            <li><i class="fas fa-check text-success me-2"></i>分析：奇偶比、分区比等</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-circle text-primary me-2"></i>双色球</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>红球：6个号码，范围1-33</li>
                            <li><i class="fas fa-check text-success me-2"></i>蓝球：1个号码，范围1-16</li>
                            <li><i class="fas fa-check text-success me-2"></i>分析：奇偶比、分区比等</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-circle text-success me-2"></i>排列五</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>号码：5个数字，范围0-9</li>
                            <li><i class="fas fa-check text-success me-2"></i>分析：奇偶比、大小比、格式等</li>
                            <li><i class="fas fa-check text-success me-2"></i>特色：重复号码彩色显示</li>
                        </ul>
                    </div>
                </div>
                
                <div class="alert alert-warning mt-3">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>注意：</strong>本分析结果仅供参考，彩票开奖具有随机性，请理性购彩。
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // 彩票类型切换
    $('input[name="lottery_type"]').change(function() {
        const selectedType = $(this).val();
        $('.lottery-input').hide();
        $(`#${selectedType}-input`).show();
        
        // 清空结果
        $('#resultSection').hide();
        $('#analysisResult').empty();
    });
    
    // 表单提交
    $('#analysisForm').submit(function(e) {
        e.preventDefault();
        
        const lotteryType = $('input[name="lottery_type"]:checked').val();
        let redBalls = [];
        let blueBalls = [];
        
        try {
            if (lotteryType === 'dlt') {
                redBalls = parseNumbers($('#dlt-red').val(), 5);
                blueBalls = parseNumbers($('#dlt-blue').val(), 2);
                validateRange(redBalls, 1, 35, '大乐透红球');
                validateRange(blueBalls, 1, 12, '大乐透蓝球');
            } else if (lotteryType === 'ssq') {
                redBalls = parseNumbers($('#ssq-red').val(), 6);
                blueBalls = parseNumbers($('#ssq-blue').val(), 1);
                validateRange(redBalls, 1, 33, '双色球红球');
                validateRange(blueBalls, 1, 16, '双色球蓝球');
            } else if (lotteryType === 'p5') {
                redBalls = parseNumbers($('#p5-numbers').val(), 5);
                validateRange(redBalls, 0, 9, '排列五号码');
                blueBalls = []; // 排列五没有蓝球
            }
            
            // 发送分析请求
            analyzeNumbers(lotteryType, redBalls, blueBalls);
            
        } catch (error) {
            showError(error.message);
        }
    });
    
    // 解析号码
    function parseNumbers(input, expectedCount) {
        if (!input || input.trim() === '') {
            throw new Error('请输入号码');
        }
        
        const numbers = input.split(',').map(n => {
            const num = parseInt(n.trim());
            if (isNaN(num)) {
                throw new Error(`无效的号码: ${n}`);
            }
            return num;
        });
        
        if (numbers.length !== expectedCount) {
            throw new Error(`需要${expectedCount}个号码，实际输入了${numbers.length}个`);
        }
        
        return numbers;
    }
    
    // 验证号码范围
    function validateRange(numbers, min, max, type) {
        for (let num of numbers) {
            if (num < min || num > max) {
                throw new Error(`${type}范围应为${min}-${max}，但输入了${num}`);
            }
        }
    }
    
    // 分析号码
    function analyzeNumbers(lotteryType, redBalls, blueBalls) {
        const submitBtn = $('#analysisForm button[type="submit"]');
        const originalText = submitBtn.html();
        
        showLoading(submitBtn);
        
        $.ajax({
            url: '/api/analyze',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                lottery_type: lotteryType,
                red_balls: redBalls,
                blue_balls: blueBalls
            }),
            success: function(response) {
                displayResult(response.result);
                $('#resultSection').show();
                $('html, body').animate({
                    scrollTop: $('#resultSection').offset().top - 100
                }, 500);
            },
            error: function(xhr) {
                const error = xhr.responseJSON ? xhr.responseJSON.error : '分析失败';
                showError(error);
            },
            complete: function() {
                hideLoading(submitBtn, originalText);
            }
        });
    }
    
    // 显示分析结果
    function displayResult(result) {
        let html = '';
        
        // 号码显示
        html += '<div class="row mb-4">';
        html += '<div class="col-12">';
        html += '<h6><i class="fas fa-list-ol me-2"></i>输入号码</h6>';
        html += '<div class="mb-3">';
        
        if (result.lottery_type === 'p5') {
            result.numbers.forEach(num => {
                html += formatNumber(num.value, num.is_repeat);
            });
        } else {
            html += '<div class="mb-2">';
            html += '<span class="me-2">红球：</span>';
            result.red_balls.forEach(num => {
                html += formatNumber(num);
            });
            html += '</div>';
            
            html += '<div>';
            html += '<span class="me-2">蓝球：</span>';
            result.blue_balls.forEach(num => {
                html += `<span class="number-ball blue-ball">${String(num).padStart(2, '0')}</span>`;
            });
            html += '</div>';
        }
        
        html += '</div>';
        html += '</div>';
        html += '</div>';
        
        // 分析结果
        html += '<div class="row">';
        html += '<div class="col-md-6">';
        html += '<h6><i class="fas fa-chart-pie me-2"></i>分析数据</h6>';
        html += '<table class="table table-sm">';
        
        if (result.lottery_type === 'p5') {
            html += `<tr><td>号码格式</td><td><code>${result.analysis.format}</code></td></tr>`;
            html += `<tr><td>奇偶比</td><td>${result.analysis.odd_even_ratio}</td></tr>`;
            html += `<tr><td>大小比</td><td>${result.analysis.big_small_ratio}</td></tr>`;
            html += `<tr><td>连号数</td><td>${result.analysis.consecutive}</td></tr>`;
            html += `<tr><td>重号数</td><td>${result.analysis.repeat_count}</td></tr>`;
        } else {
            html += `<tr><td>奇偶比</td><td>${result.analysis.odd_even_ratio}</td></tr>`;
            html += `<tr><td>奇偶排布</td><td><code>${result.analysis.odd_even_pattern}</code></td></tr>`;
            html += `<tr><td>分区比</td><td>${result.analysis.zone_ratio}</td></tr>`;
            html += `<tr><td>分区排布</td><td><code>${result.analysis.zone_pattern}</code></td></tr>`;
        }
        
        html += '</table>';
        html += '</div>';
        
        // 综合评估
        html += '<div class="col-md-6">';
        html += '<h6><i class="fas fa-star me-2"></i>综合评估</h6>';
        
        result.evaluation.forEach(item => {
            const isSuccess = item.startsWith('✓');
            const cssClass = isSuccess ? 'evaluation-success' : 'evaluation-warning';
            html += `<div class="evaluation-item ${cssClass}">${item}</div>`;
        });
        
        html += '</div>';
        html += '</div>';
        
        $('#analysisResult').html(html);
    }
});
</script>
{% endblock %}
