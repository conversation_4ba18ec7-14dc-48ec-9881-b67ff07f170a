{% extends "base.html" %}

{% block title %}数据上传 - 彩票分析系统{% endblock %}

{% block content %}
<div class="row">
    <!-- 页面标题 -->
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2>
                    <i class="fas fa-upload text-info me-2"></i>
                    数据上传
                </h2>
                <p class="text-muted mb-0">上传CSV格式的彩票数据文件进行批量分析</p>
            </div>
        </div>
    </div>
</div>

<!-- 上传表单 -->
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-csv me-2"></i>CSV文件上传
                </h5>
            </div>
            <div class="card-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    <!-- 彩票类型选择 -->
                    <div class="mb-4">
                        <label class="form-label">选择彩票类型</label>
                        <div class="btn-group w-100" role="group">
                            <input type="radio" class="btn-check" name="lottery_type" id="upload-dlt" value="dlt" checked>
                            <label class="btn btn-outline-primary" for="upload-dlt">
                                <i class="fas fa-circle text-danger me-2"></i>大乐透
                            </label>
                            
                            <input type="radio" class="btn-check" name="lottery_type" id="upload-ssq" value="ssq">
                            <label class="btn btn-outline-primary" for="upload-ssq">
                                <i class="fas fa-circle text-primary me-2"></i>双色球
                            </label>
                            
                            <input type="radio" class="btn-check" name="lottery_type" id="upload-p5" value="p5">
                            <label class="btn btn-outline-primary" for="upload-p5">
                                <i class="fas fa-circle text-success me-2"></i>排列五
                            </label>
                        </div>
                    </div>
                    
                    <!-- 文件选择 -->
                    <div class="mb-4">
                        <label for="csvFile" class="form-label">选择CSV文件</label>
                        <input type="file" class="form-control" id="csvFile" name="file" accept=".csv" required>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            请选择符合格式要求的CSV文件，文件大小不超过10MB
                        </div>
                    </div>
                    
                    <!-- 文件预览 -->
                    <div id="filePreview" class="mb-4" style="display: none;">
                        <h6><i class="fas fa-eye me-2"></i>文件预览</h6>
                        <div class="border rounded p-3 bg-light">
                            <div id="fileInfo"></div>
                        </div>
                    </div>
                    
                    <!-- 上传按钮 -->
                    <div class="text-center">
                        <button type="submit" class="btn btn-lottery btn-lg">
                            <i class="fas fa-cloud-upload-alt me-2"></i>开始上传
                        </button>
                    </div>
                </form>
                
                <!-- 上传进度 -->
                <div id="uploadProgress" class="mt-4" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%"></div>
                    </div>
                    <div class="text-center mt-2">
                        <small class="text-muted">正在处理文件...</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 格式说明 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card analysis-card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-alt me-2"></i>CSV格式说明
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6><i class="fas fa-circle text-danger me-2"></i>大乐透格式</h6>
                        <div class="bg-light p-3 rounded">
                            <code>
                                日期,期号,红球1,红球2,红球3,红球4,红球5,蓝球1,蓝球2<br>
                                2024-01-01,24001,01,12,23,28,35,03,11<br>
                                2024-01-03,24002,05,15,18,25,33,02,09
                            </code>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-circle text-primary me-2"></i>双色球格式</h6>
                        <div class="bg-light p-3 rounded">
                            <code>
                                日期,期号,红球1,红球2,红球3,红球4,红球5,红球6,蓝球<br>
                                2024-01-02,24001,01,05,12,18,25,33,08<br>
                                2024-01-04,24002,03,09,15,21,28,31,12
                            </code>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-circle text-success me-2"></i>排列五格式</h6>
                        <div class="bg-light p-3 rounded">
                            <code>
                                日期,期号,号码1,号码2,号码3,号码4,号码5<br>
                                2024-01-01,24001,1,2,3,4,5<br>
                                2024-01-02,24002,6,7,8,9,0
                            </code>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info mt-3">
                    <h6><i class="fas fa-lightbulb me-2"></i>格式要求</h6>
                    <ul class="mb-0">
                        <li>文件必须是UTF-8编码的CSV格式</li>
                        <li>第一行必须是列标题（如上所示）</li>
                        <li>日期格式：YYYY-MM-DD</li>
                        <li>期号格式：数字（如24001）</li>
                        <li>号码格式：数字，可以带前导零</li>
                        <li>文件大小不超过10MB</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 上传历史 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>最近上传记录
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>彩票类型</th>
                                <th>文件名</th>
                                <th>记录数</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody id="uploadHistory">
                            <tr>
                                <td colspan="5" class="text-center text-muted">暂无上传记录</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // 文件选择事件
    $('#csvFile').change(function() {
        const file = this.files[0];
        if (file) {
            showFilePreview(file);
        } else {
            $('#filePreview').hide();
        }
    });
    
    // 显示文件预览
    function showFilePreview(file) {
        const fileSize = (file.size / 1024 / 1024).toFixed(2);
        const fileInfo = `
            <div class="row">
                <div class="col-md-6">
                    <strong>文件名：</strong>${file.name}<br>
                    <strong>文件大小：</strong>${fileSize} MB<br>
                    <strong>文件类型：</strong>${file.type || 'text/csv'}
                </div>
                <div class="col-md-6">
                    <strong>最后修改：</strong>${new Date(file.lastModified).toLocaleString()}<br>
                    <strong>编码建议：</strong>UTF-8<br>
                    <strong>分隔符：</strong>逗号(,)
                </div>
            </div>
        `;
        
        $('#fileInfo').html(fileInfo);
        $('#filePreview').show();
        
        // 验证文件大小
        if (file.size > 10 * 1024 * 1024) {
            showError('文件大小不能超过10MB');
            $('#csvFile').val('');
            $('#filePreview').hide();
        }
    }
    
    // 表单提交
    $('#uploadForm').submit(function(e) {
        e.preventDefault();
        
        const formData = new FormData();
        const file = $('#csvFile')[0].files[0];
        const lotteryType = $('input[name="lottery_type"]:checked').val();
        
        if (!file) {
            showError('请选择要上传的文件');
            return;
        }
        
        formData.append('file', file);
        formData.append('lottery_type', lotteryType);
        
        uploadFile(formData);
    });
    
    // 上传文件
    function uploadFile(formData) {
        const submitBtn = $('#uploadForm button[type="submit"]');
        const originalText = submitBtn.html();
        
        // 显示进度条
        $('#uploadProgress').show();
        $('.progress-bar').css('width', '0%');
        
        showLoading(submitBtn);
        
        $.ajax({
            url: '/api/upload',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            xhr: function() {
                const xhr = new window.XMLHttpRequest();
                xhr.upload.addEventListener('progress', function(e) {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        $('.progress-bar').css('width', percentComplete + '%');
                    }
                }, false);
                return xhr;
            },
            success: function(response) {
                showSuccess(`上传成功！处理了 ${response.count} 条记录`);
                
                // 添加到上传历史
                addUploadHistory({
                    time: new Date().toLocaleString(),
                    lottery_type: $('input[name="lottery_type"]:checked').val(),
                    filename: $('#csvFile')[0].files[0].name,
                    count: response.count,
                    status: '成功'
                });
                
                // 重置表单
                $('#uploadForm')[0].reset();
                $('#filePreview').hide();
                
                // 3秒后跳转到分析页面
                setTimeout(function() {
                    const lotteryType = $('input[name="lottery_type"]:checked').val();
                    window.location.href = `/analysis/${lotteryType}`;
                }, 3000);
            },
            error: function(xhr) {
                const error = xhr.responseJSON ? xhr.responseJSON.error : '上传失败';
                showError(error);
                
                // 添加到上传历史
                addUploadHistory({
                    time: new Date().toLocaleString(),
                    lottery_type: $('input[name="lottery_type"]:checked').val(),
                    filename: $('#csvFile')[0].files[0].name,
                    count: 0,
                    status: '失败'
                });
            },
            complete: function() {
                hideLoading(submitBtn, originalText);
                $('#uploadProgress').hide();
            }
        });
    }
    
    // 添加上传历史记录
    function addUploadHistory(record) {
        const tbody = $('#uploadHistory');
        
        // 如果是第一条记录，清空提示文本
        if (tbody.find('td[colspan="5"]').length > 0) {
            tbody.empty();
        }
        
        const statusClass = record.status === '成功' ? 'text-success' : 'text-danger';
        const statusIcon = record.status === '成功' ? 'fa-check-circle' : 'fa-times-circle';
        
        const row = `
            <tr>
                <td>${record.time}</td>
                <td>${getLotteryTypeName(record.lottery_type)}</td>
                <td>${record.filename}</td>
                <td>${record.count}</td>
                <td class="${statusClass}">
                    <i class="fas ${statusIcon} me-1"></i>${record.status}
                </td>
            </tr>
        `;
        
        tbody.prepend(row);
        
        // 只保留最近10条记录
        tbody.find('tr').slice(10).remove();
    }
    
    // 获取彩票类型名称
    function getLotteryTypeName(type) {
        const names = {
            'dlt': '大乐透',
            'ssq': '双色球',
            'p5': '排列五'
        };
        return names[type] || type;
    }
});
</script>
{% endblock %}
