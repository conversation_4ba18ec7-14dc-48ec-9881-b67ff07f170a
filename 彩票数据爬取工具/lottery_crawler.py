#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彩票数据爬取工具 - 统一入口
重构后的统一架构，支持双色球、大乐透、排列五等多种彩票类型
"""

import argparse
import sys
from typing import List, Optional
from loguru import logger

from crawlers import CrawlerFactory
from config.lottery_config import config_manager
from utils.common import print_banner

def setup_logging():
    """设置日志配置"""
    logger.remove()  # 移除默认处理器
    logger.add(
        sys.stderr,
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name}:{function}:{line} - {message}",
        level="INFO"
    )
    logger.add(
        "logs/lottery_crawler.log",
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name}:{function}:{line} - {message}",
        level="DEBUG",
        rotation="10 MB",
        retention="30 days"
    )

def show_menu():
    """显示主菜单"""
    print("\n📋 请选择彩票类型：")
    print("1. 双色球 (SSQ)")
    print("2. 大乐透 (DLT)")
    print("3. 排列五 (PL5)")
    print("4. 同时爬取所有类型")
    print("0. 退出程序")
    print("-" * 30)

def get_user_choice(prompt: str, valid_choices: List[int]) -> str:
    """获取用户选择"""
    while True:
        try:
            choice = input(prompt).strip()
            if choice == "":
                continue
            choice_int = int(choice)
            if choice_int in valid_choices:
                return choice
            else:
                print(f"❌ 请输入有效选择: {valid_choices}")
        except ValueError:
            print("❌ 请输入数字")
        except KeyboardInterrupt:
            print("\n\n👋 程序已退出")
            sys.exit(0)

def interactive_mode():
    """交互式模式"""
    print_banner("彩票数据爬取工具", "v3.0")
    
    while True:
        show_menu()
        choice = get_user_choice("请输入选择 (0-4): ", [0, 1, 2, 3, 4])
        
        if choice == "0":
            print("👋 感谢使用，再见！")
            break
        
        # 确定要爬取的彩票类型
        lottery_types = []
        if choice == "1":
            lottery_types = ["ssq"]
        elif choice == "2":
            lottery_types = ["dlt"]
        elif choice == "3":
            lottery_types = ["pl5"]
        elif choice == "4":
            lottery_types = ["ssq", "dlt", "pl5"]
        
        # 执行爬取
        success_count = 0
        total_count = len(lottery_types)
        
        for lottery_type in lottery_types:
            config = config_manager.get_config(lottery_type)
            if not config:
                print(f"❌ 不支持的彩票类型: {lottery_type}")
                continue
            
            try:
                print(f"\n🚀 开始爬取{config.name}数据...")
                
                # 创建爬取器
                crawler = CrawlerFactory.create_crawler(lottery_type)
                if not crawler:
                    print(f"❌ 创建{config.name}爬取器失败")
                    continue
                
                # 显示当前最新期号
                latest_period = crawler.get_latest_period_from_web()
                if latest_period:
                    print(f"📈 {config.name}最新期号：{latest_period}")

                # 询问爬取模式
                print(f"\n请选择{config.name}爬取模式：")
                print("1. 增量更新（只爬取新数据）")
                print("2. 全量爬取（爬取所有历史数据）")
                mode_choice = get_user_choice("请选择模式 (1-2): ", [1, 2])

                if mode_choice == "1":
                    # 增量更新模式
                    success = crawler.incremental_update()
                else:
                    # 全量爬取模式（参考get_data.py）
                    print(f"🚀 开始爬取{config.name}所有历史数据...")
                    success = crawler.full_crawl(overwrite=True)

                if success:
                    print(f"✅ {config.name}数据爬取完成！")
                    success_count += 1
                else:
                    print(f"❌ {config.name}数据爬取失败")
                
            except Exception as e:
                print(f"❌ {config.name}数据爬取失败：{e}")
                logger.error(f"{config.name}数据爬取失败: {e}")
        
        # 显示总结
        print(f"\n📊 爬取完成！成功: {success_count}/{total_count}")
        
        # 询问是否继续
        continue_choice = input("\n是否继续爬取其他数据？(y/N): ").strip().lower()
        if continue_choice not in ['y', 'yes', '是']:
            print("👋 感谢使用，再见！")
            break

def command_line_mode(args):
    """命令行模式"""
    try:
        print_banner("彩票数据爬取工具", "v3.0")

        # 验证彩票类型
        if not config_manager.is_supported(args.type):
            print(f"❌ 不支持的彩票类型: {args.type}")
            print(f"支持的类型: {', '.join(config_manager.get_supported_types())}")
            sys.exit(1)

        config = config_manager.get_config(args.type)
        print(f"🚀 开始爬取{config.name}数据...")

        # 创建爬取器
        crawler = CrawlerFactory.create_crawler(args.type)
        if not crawler:
            print(f"❌ 创建{config.name}爬取器失败")
            sys.exit(1)

        # 显示当前最新期号
        latest_period = crawler.get_latest_period_from_web()
        if latest_period:
            print(f"📈 {config.name}最新期号：{latest_period}")

        # 根据参数选择爬取模式
        if args.incremental:
            # 增量更新模式
            success = crawler.incremental_update()
        elif args.all:
            # 全量爬取所有数据（参考get_data.py）
            print(f"🚀 开始爬取{config.name}所有历史数据...")
            success = crawler.full_crawl(overwrite=True)  # 覆盖模式
        else:
            # 指定范围爬取模式
            success = crawler.full_crawl(
                start_period=args.start,
                end_period=args.end,
                count=args.count,
                overwrite=args.overwrite
            )

        if success:
            print(f"✅ {config.name}数据爬取完成！")

            # 显示数据信息
            data_info = crawler.get_data_info()
            print(f"📊 数据统计: 共 {data_info.get('total_records', 0)} 期数据")
            print(f"📁 数据文件: {data_info.get('file_path', 'N/A')}")
        else:
            print(f"❌ {config.name}数据爬取失败")
            sys.exit(1)

    except Exception as e:
        logger.error(f"命令行模式执行失败: {e}")
        print(f"❌ 程序执行失败: {e}")
        sys.exit(1)

def batch_mode(lottery_types: List[str], args):
    """批量模式"""
    try:
        print_banner("彩票数据爬取工具 - 批量模式", "v3.0")
        
        success_count = 0
        total_count = len(lottery_types)
        
        for lottery_type in lottery_types:
            if not config_manager.is_supported(lottery_type):
                print(f"❌ 跳过不支持的彩票类型: {lottery_type}")
                continue
            
            config = config_manager.get_config(lottery_type)
            print(f"\n🚀 开始处理{config.name}数据...")
            
            try:
                # 创建爬取器
                crawler = CrawlerFactory.create_crawler(lottery_type)
                if not crawler:
                    print(f"❌ 创建{config.name}爬取器失败")
                    continue
                
                # 执行爬取
                if args.incremental:
                    success = crawler.incremental_update()
                else:
                    success = crawler.full_crawl(
                        start_period=args.start,
                        end_period=args.end,
                        count=args.count
                    )
                
                if success:
                    print(f"✅ {config.name}数据处理完成")
                    success_count += 1
                else:
                    print(f"❌ {config.name}数据处理失败")
                    
            except Exception as e:
                print(f"❌ {config.name}数据处理失败: {e}")
                logger.error(f"{config.name}数据处理失败: {e}")
        
        print(f"\n📊 批量处理完成！成功: {success_count}/{total_count}")
        
        if success_count == 0:
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"批量模式执行失败: {e}")
        print(f"❌ 批量处理失败: {e}")
        sys.exit(1)

def main():
    """主函数"""
    # 设置日志
    setup_logging()
    
    # 命令行参数解析
    parser = argparse.ArgumentParser(
        description="彩票数据爬取工具 v3.0 - 统一架构设计",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 交互式模式
  python lottery_crawler.py

  # 增量更新双色球数据
  python lottery_crawler.py --type ssq --incremental

  # 全量爬取大乐透所有历史数据（参考get_data.py）
  python lottery_crawler.py --type dlt --all

  # 爬取大乐透最新100期数据
  python lottery_crawler.py --type dlt --count 100

  # 批量增量更新所有彩票数据
  python lottery_crawler.py --batch ssq dlt pl5 --incremental
        """
    )
    
    parser.add_argument('--type', type=str, choices=config_manager.get_supported_types(),
                       help="彩票类型")
    parser.add_argument('--start', type=int, help="开始期号")
    parser.add_argument('--end', type=int, help="结束期号")
    parser.add_argument('--count', type=int, help="爬取最新N期数据")
    parser.add_argument('--incremental', action='store_true', help="增量更新模式")
    parser.add_argument('--all', action='store_true', help="全量爬取所有历史数据")
    parser.add_argument('--overwrite', action='store_true', help="覆盖现有数据文件")
    parser.add_argument('--batch', nargs='+', choices=config_manager.get_supported_types(),
                       help="批量处理多种彩票类型")
    
    args = parser.parse_args()
    
    try:
        if args.batch:
            # 批量模式
            batch_mode(args.batch, args)
        elif args.type:
            # 命令行模式
            command_line_mode(args)
        else:
            # 交互式模式
            interactive_mode()
            
    except KeyboardInterrupt:
        print("\n\n👋 程序已退出")
        sys.exit(0)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        print(f"❌ 程序执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
